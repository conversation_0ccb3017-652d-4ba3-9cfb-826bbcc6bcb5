09:01:02.933 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:01:02.939 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:02:15.751 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 500640 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
10:02:15.753 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:02:16.530 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
10:02:16.530 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:02:16.530 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:02:16.575 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:02:16.998 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:02:17.200 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:02:17.486 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:02:17.487 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:02:17.817 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:02:17.844 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:02:17.845 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:02:17.909 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:02:17.914 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:02:17.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:02:17.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:02:17.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:02:17.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:02:17.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:02:17.917 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:02:17.917 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:02:17.918 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:02:17.953 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
10:02:17.967 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.526 seconds (JVM running for 2.845)
10:02:48.846 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:10:25.497 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
check_user_name
from drl_work_check_records
where car_id = ?
order by sort
10:10:25.497 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：null
10:10:54.009 [http-nio-9550-exec-10] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
check_user_name
from drl_work_check_records
where car_id = ?
order by sort
10:10:54.010 [http-nio-9550-exec-10] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:16:21.789 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:16:21.790 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:36:53.643 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:36:53.643 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:37:05.722 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:37:05.722 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:37:19.508 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:37:19.508 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:37:37.118 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:37:37.118 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:48:07.506 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:48:07.506 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:53:20.409 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:53:20.409 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
11:13:32.861 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
11:13:32.861 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
11:13:32.862 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1533] - 开始导出新JSON格式Word文档，表格标题: null
11:13:33.194 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,761] - 已设置文档为横向纸张
11:13:33.194 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1577] - 创建新JSON格式表格，总行数: 2, 总列数: 8
11:13:33.264 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1648] - 设置表格总宽度: 935px (14025twips)
11:13:33.341 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1754] - 应用JSON格式表头合并单元格，数量: 7
11:13:33.343 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:13:33.347 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:13:33.348 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:13:33.349 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:13:33.352 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:13:33.353 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:13:33.354 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:13:33.433 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1554] - 新JSON格式Word文档导出完成，文件大小: 2799 bytes
11:13:33.441 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_111333.docx, 大小: 2799 bytes
11:15:44.983 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
11:15:44.984 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
11:15:44.985 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1533] - 开始导出新JSON格式Word文档，表格标题: null
11:15:44.988 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,761] - 已设置文档为横向纸张
11:15:44.989 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1577] - 创建新JSON格式表格，总行数: 2, 总列数: 8
11:15:44.990 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1648] - 设置表格总宽度: 810px (12150twips)
11:15:45.028 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1754] - 应用JSON格式表头合并单元格，数量: 7
11:15:45.030 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:15:45.032 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:15:45.033 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:15:45.034 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:15:45.035 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:15:45.036 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:15:45.037 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:15:45.051 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1554] - 新JSON格式Word文档导出完成，文件大小: 2798 bytes
11:15:45.061 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_111545.docx, 大小: 2798 bytes
11:17:07.830 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
11:17:07.832 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
11:17:07.834 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1533] - 开始导出新JSON格式Word文档，表格标题: null
11:17:07.837 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,761] - 已设置文档为横向纸张
11:17:07.838 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1577] - 创建新JSON格式表格，总行数: 2, 总列数: 8
11:17:07.842 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1648] - 设置表格总宽度: 840px (12600twips)
11:17:07.851 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1754] - 应用JSON格式表头合并单元格，数量: 7
11:17:07.854 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:17:07.855 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:17:07.860 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:17:07.862 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:17:07.863 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:17:07.864 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:17:07.867 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:17:07.881 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1554] - 新JSON格式Word文档导出完成，文件大小: 2798 bytes
11:17:07.886 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_111707.docx, 大小: 2798 bytes
11:19:18.587 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
11:19:18.588 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
11:19:18.588 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1533] - 开始导出新JSON格式Word文档，表格标题: null
11:19:18.589 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,761] - 已设置文档为横向纸张
11:19:18.589 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1577] - 创建新JSON格式表格，总行数: 2, 总列数: 8
11:19:18.591 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1648] - 设置表格总宽度: 895px (13425twips)
11:19:18.595 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1754] - 应用JSON格式表头合并单元格，数量: 7
11:19:18.596 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:19:18.597 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:19:18.598 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:19:18.598 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:19:18.599 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:19:18.599 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:19:18.599 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:19:18.605 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1554] - 新JSON格式Word文档导出完成，文件大小: 2799 bytes
11:19:18.609 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_111918.docx, 大小: 2799 bytes
11:20:21.325 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
11:20:21.326 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
11:20:21.326 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1533] - 开始导出新JSON格式Word文档，表格标题: null
11:20:21.327 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,761] - 已设置文档为横向纸张
11:20:21.328 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1577] - 创建新JSON格式表格，总行数: 2, 总列数: 8
11:20:21.329 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1648] - 设置表格总宽度: 880px (13200twips)
11:20:21.332 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1754] - 应用JSON格式表头合并单元格，数量: 7
11:20:21.333 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:20:21.334 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:20:21.335 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:20:21.337 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:20:21.338 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:20:21.339 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:20:21.340 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:20:21.344 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1554] - 新JSON格式Word文档导出完成，文件大小: 2798 bytes
11:20:21.347 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_112021.docx, 大小: 2798 bytes
13:10:05.644 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:10:05.655 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:10:13.524 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 725733 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:10:13.527 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:10:16.423 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:10:16.424 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:10:16.424 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:10:16.483 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:10:17.130 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:10:17.401 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:10:17.880 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
13:10:17.882 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
13:10:18.390 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
13:10:18.427 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
13:10:18.428 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
13:10:18.524 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
13:10:18.531 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
13:10:18.534 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
13:10:18.534 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
13:10:18.534 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
13:10:18.534 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
13:10:18.534 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
13:10:18.534 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
13:10:18.536 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
13:10:18.537 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
13:10:18.585 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:10:18.608 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.882 seconds (JVM running for 4.388)
13:10:22.577 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:10:30.628 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
13:10:30.629 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 3, 数据合并数量: 1
13:10:30.629 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
13:10:30.813 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
13:10:30.813 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 3, 总列数: 6
13:10:30.867 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 620px (9300twips)
13:10:30.929 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 60px (900twips)
13:10:30.931 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 3
13:10:30.932 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '产品名称'
13:10:30.935 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '生产信息'
13:10:30.936 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列1-2, 跨度2列
13:10:30.939 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列1-2
13:10:30.940 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-5], 跨行: 1, 跨列: 3, 内容: '责任人员'
13:10:30.941 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-5, 跨度3列
13:10:30.942 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-5
13:10:30.942 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1801] - 应用JSON格式数据行合并单元格，数量: 1，表头偏移: 2
13:10:31.025 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 2877 bytes
13:10:31.032 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_131031.docx, 大小: 2877 bytes
13:11:21.090 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
13:11:21.091 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
13:11:21.092 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
13:11:21.095 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
13:11:21.096 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 2, 总列数: 8
13:11:21.098 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
13:11:21.106 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
13:11:21.107 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
13:11:21.108 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
13:11:21.109 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
13:11:21.110 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
13:11:21.110 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
13:11:21.111 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
13:11:21.112 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
13:11:21.114 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
13:11:21.115 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
13:11:21.121 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 2788 bytes
13:11:21.125 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_131121.docx, 大小: 2788 bytes
13:13:55.252 [http-nio-9550-exec-4] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
13:13:55.253 [http-nio-9550-exec-4] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 5, 数据合并数量: 1
13:13:55.253 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
13:13:55.254 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
13:13:55.255 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 5, 总列数: 8
13:13:55.257 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 920px (13800twips)
13:13:55.260 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 60px (900twips)
13:13:55.261 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 60px (900twips)
13:13:55.262 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 60px (900twips)
13:13:55.263 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
13:13:55.264 [http-nio-9550-exec-4] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
13:13:55.511 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 5
13:13:55.512 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '产品名称'
13:13:55.515 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '规格型号'
13:13:55.516 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '质量检验'
13:13:55.517 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '生产信息'
13:13:55.517 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
13:13:55.518 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
13:13:55.519 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[5-7], 跨行: 1, 跨列: 3, 内容: '责任人员'
13:13:55.519 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列5-7, 跨度3列
13:13:55.519 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列5-7
13:13:55.520 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1801] - 应用JSON格式数据行合并单元格，数量: 1，表头偏移: 2
13:13:55.520 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,1873] - 数据行合并使用绝对索引，不需要偏移: 2
13:13:55.521 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,1888] - 应用数据行合并: 行[2-3], 列[0-0], 跨行: 2, 跨列: 1, 内容: '智能手机
（多功能检测）'
13:13:55.529 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3227 bytes
13:13:55.532 [http-nio-9550-exec-4] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_131355.docx, 大小: 3227 bytes
13:18:15.219 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
13:18:15.220 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 1
13:18:15.220 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
13:18:15.222 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
13:18:15.223 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 5, 总列数: 8
13:18:15.234 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 920px (13800twips)
13:18:15.248 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 60px (900twips)
13:18:15.249 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 60px (900twips)
13:18:15.250 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 60px (900twips)
13:18:15.251 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
13:18:15.252 [http-nio-9550-exec-7] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
13:18:15.380 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
13:18:15.381 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '产品名称'
13:18:15.381 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '规格型号'
13:18:15.382 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '质量检验'
13:18:15.382 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '生产信息'
13:18:15.382 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
13:18:15.382 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
13:18:15.383 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作者'
13:18:15.383 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
13:18:15.383 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
13:18:15.384 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1801] - 应用JSON格式数据行合并单元格，数量: 1，表头偏移: 2
13:18:15.384 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,1873] - 数据行合并使用绝对索引，不需要偏移: 2
13:18:15.384 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,1888] - 应用数据行合并: 行[2-3], 列[0-0], 跨行: 2, 跨列: 1, 内容: '智能手机
（多功能检测）'
13:18:15.389 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3193 bytes
13:18:15.392 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_131815.docx, 大小: 3193 bytes
13:54:20.418 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 778294 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:54:20.421 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:54:21.202 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:54:21.202 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:54:21.203 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:54:21.243 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:54:21.647 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:54:21.868 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:54:22.122 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
13:54:22.123 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
13:54:22.421 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
13:54:22.461 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
13:54:22.462 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
13:54:22.540 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
13:54:22.545 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
13:54:22.547 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
13:54:22.548 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
13:54:22.548 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
13:54:22.548 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
13:54:22.548 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
13:54:22.548 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
13:54:22.549 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
13:54:22.551 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
13:54:22.586 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:54:22.701 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:54:22.703 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:54:22.705 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9550"]
13:54:22.705 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
13:54:22.708 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9550"]
13:54:22.709 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9550"]
13:54:48.585 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 779207 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:54:48.587 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:54:49.265 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:54:49.266 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:54:49.266 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:54:49.304 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:54:49.716 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:54:49.936 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:54:50.223 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
13:54:50.223 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
13:54:50.492 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
13:54:50.515 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
13:54:50.517 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
13:54:50.587 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
13:54:50.592 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
13:54:50.594 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
13:54:50.595 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
13:54:50.595 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
13:54:50.595 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
13:54:50.595 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
13:54:50.595 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
13:54:50.596 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
13:54:50.598 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
13:54:50.636 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:54:50.651 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.384 seconds (JVM running for 2.711)
13:54:51.207 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:07:01.209 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 794583 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
14:07:01.211 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:07:02.083 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
14:07:02.083 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:07:02.083 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:07:02.121 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:07:02.517 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:07:02.714 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:07:02.960 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:07:02.961 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:07:03.191 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:07:03.212 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:07:03.213 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:07:03.270 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:07:03.274 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:07:03.276 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:07:03.276 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:07:03.276 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:07:03.276 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:07:03.277 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:07:03.277 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:07:03.277 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:07:03.278 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:07:03.313 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
14:07:03.428 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:07:03.431 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:07:03.433 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9550"]
14:07:03.434 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:07:03.436 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9550"]
14:07:03.437 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9550"]
14:07:22.682 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
14:07:22.683 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 1
14:07:22.683 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
14:07:22.827 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
14:07:22.827 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 3, 总列数: 8
14:07:22.865 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
14:07:22.909 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
14:07:22.911 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.脱漆
2.冲洗__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:07:22.912 [http-nio-9550-exec-7] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:07:23.165 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
14:07:23.166 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:07:23.169 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:07:23.170 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:07:23.171 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:07:23.171 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
14:07:23.172 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
14:07:23.173 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:07:23.173 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:07:23.174 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:07:23.174 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1801] - 应用JSON格式数据行合并单元格，数量: 1，表头偏移: 2
14:07:23.225 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 2996 bytes
14:07:23.231 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_140723.docx, 大小: 2996 bytes
14:08:34.550 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
14:08:34.551 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 1
14:08:34.551 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
14:08:34.552 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
14:08:34.553 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 4, 总列数: 8
14:08:34.554 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
14:08:34.558 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
14:08:34.559 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.脱漆
2.冲洗__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:08:34.559 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:08:34.641 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
14:08:34.642 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
14:08:34.642 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:08:34.643 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:08:34.644 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:08:34.644 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:08:34.644 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
14:08:34.644 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
14:08:34.644 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:08:34.645 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:08:34.645 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:08:34.645 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1801] - 应用JSON格式数据行合并单元格，数量: 1，表头偏移: 2
14:08:34.645 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,1873] - 数据行合并使用绝对索引，不需要偏移: 2
14:08:34.646 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,1888] - 应用数据行合并: 行[2-3], 列[0-0], 跨行: 2, 跨列: 1, 内容: '脱漆'
14:08:34.651 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3017 bytes
14:08:34.654 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_140834.docx, 大小: 3017 bytes
14:13:47.514 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
14:13:47.514 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
14:54:46.740 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
14:54:46.745 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 1
14:54:46.745 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
14:54:46.750 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
14:54:46.751 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 4, 总列数: 8
14:54:46.756 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
14:54:46.765 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
14:54:46.766 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.脱漆
2.冲洗__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:54:46.766 [http-nio-9550-exec-7] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:54:46.985 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
14:54:46.985 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
14:54:46.986 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:54:46.987 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:54:46.987 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:54:46.987 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:54:46.988 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
14:54:46.988 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
14:54:46.988 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:54:46.989 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:54:46.989 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:54:46.989 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1801] - 应用JSON格式数据行合并单元格，数量: 1，表头偏移: 2
14:54:46.990 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,1873] - 数据行合并使用绝对索引，不需要偏移: 2
14:54:46.990 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,1888] - 应用数据行合并: 行[2-3], 列[0-0], 跨行: 2, 跨列: 1, 内容: '脱漆'
14:54:47.001 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3018 bytes
14:54:47.006 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_145447.docx, 大小: 3018 bytes
16:45:20.989 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
16:45:20.997 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
16:45:20.997 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
16:45:21.006 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
16:45:21.007 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:45:21.012 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
16:45:21.019 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
16:45:21.020 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.脱漆
2.冲洗__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:45:21.020 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:45:21.257 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
16:45:21.258 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:45:21.259 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:45:21.260 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:45:21.260 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:45:21.261 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
16:45:21.261 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
16:45:21.261 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:45:21.262 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:45:21.263 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:45:21.272 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 2996 bytes
16:45:21.279 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_164521.docx, 大小: 2996 bytes
17:01:48.959 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
17:01:48.960 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 1
17:01:48.960 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
17:01:48.961 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
17:01:48.961 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 6, 总列数: 8
17:01:48.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
17:01:48.968 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
17:01:48.968 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi><mo>=</mo><mrow><mi>m</mi><mo>&#x2062;</mo><msup><mi>c</mi><mn>2</mn></msup></mrow></math>}
17:01:48.969 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:01:49.175 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
17:01:49.176 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>&#x3C0;</mi><mo>&#x2062;</mo><msup><mi>r</mi><mn>2</mn></msup></math>}
17:01:49.176 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:01:49.365 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
17:01:49.366 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><msub><mi>x</mi><mi>i</mi></msub></math>}
17:01:49.366 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:01:49.551 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
17:01:49.551 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msubsup><mo>&#x222B;</mo><mn>0</mn><mn>1</mn></msubsup><mi>x</mi><mrow><mi>d</mi><mi>x</mi></mrow></mrow><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math>}
17:01:49.552 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:01:49.679 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
17:01:49.680 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:01:49.680 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:01:49.680 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:01:49.681 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:01:49.681 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
17:01:49.681 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
17:01:49.681 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:01:49.681 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:01:49.682 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:01:49.682 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1801] - 应用JSON格式数据行合并单元格，数量: 1，表头偏移: 2
17:01:49.682 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,1873] - 数据行合并使用绝对索引，不需要偏移: 2
17:01:49.682 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,1888] - 应用数据行合并: 行[2-3], 列[3-4], 跨行: 2, 跨列: 2, 内容: '12月17-18日'
17:01:49.682 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行2, 列3-4, 跨度2列
17:01:49.682 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行2, 列3-4
17:01:49.688 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3325 bytes
17:01:49.691 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_170149.docx, 大小: 3325 bytes
17:02:43.500 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
17:02:43.501 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
17:02:43.501 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
17:02:43.502 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
17:02:43.502 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 3, 总列数: 8
17:02:43.504 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
17:02:43.506 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 35px (525twips)
17:02:43.507 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.脱漆
2.冲洗__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:02:43.507 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:02:43.598 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
17:02:43.598 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:02:43.599 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:02:43.599 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:02:43.599 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:02:43.599 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
17:02:43.600 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
17:02:43.600 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:02:43.600 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:02:43.601 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:02:43.605 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 2996 bytes
17:02:43.608 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_170243.docx, 大小: 2996 bytes
