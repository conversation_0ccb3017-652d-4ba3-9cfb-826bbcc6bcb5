12:13:02.631 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 16385 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
12:13:02.633 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
12:13:03.416 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
12:13:03.417 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:13:03.417 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
12:13:03.460 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:13:03.971 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
12:13:04.208 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
12:13:04.600 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
12:13:04.601 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
12:13:04.888 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
12:13:04.915 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
12:13:04.916 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
12:13:04.983 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
12:13:04.987 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
12:13:04.989 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
12:13:04.990 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
12:13:04.990 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
12:13:04.990 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
12:13:04.990 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
12:13:04.990 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
12:13:04.991 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
12:13:04.992 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
12:13:05.028 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
12:13:05.043 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.774 seconds (JVM running for 3.144)
12:13:19.952 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:14:41.563 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
12:14:41.563 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
12:18:16.103 [http-nio-9550-exec-5] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
12:18:16.104 [http-nio-9550-exec-5] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
13:25:12.444 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:25:12.450 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:25:18.062 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 104066 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:25:18.065 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:25:18.812 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:25:18.813 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:25:18.813 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:25:18.852 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:25:19.316 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:25:19.541 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:25:19.805 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
13:25:19.806 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
13:25:20.067 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
13:25:20.091 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
13:25:20.092 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
13:25:20.158 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
13:25:20.162 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
13:25:20.164 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
13:25:20.165 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
13:25:20.165 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
13:25:20.165 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
13:25:20.165 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
13:25:20.165 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
13:25:20.166 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
13:25:20.167 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
13:25:20.205 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:25:20.219 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.555 seconds (JVM running for 2.919)
13:25:22.895 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:02:52.879 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:02:52.887 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:04:47.905 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 152776 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
14:04:47.907 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:04:48.674 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
14:04:48.674 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:04:48.675 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:04:48.719 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:04:49.173 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:04:49.375 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:04:49.645 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:04:49.646 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:04:49.939 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:04:49.962 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:04:49.963 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:04:50.026 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:04:50.030 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:04:50.032 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:04:50.033 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:04:50.033 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:04:50.033 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:04:50.033 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:04:50.033 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:04:50.034 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:04:50.035 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:04:50.071 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
14:04:50.085 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.623 seconds (JVM running for 2.957)
14:05:03.291 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:24:06.136 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,109] - 验证简单Word导出请求，标题: 简单Word文档
15:24:06.138 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,145] - 简单Word导出请求验证通过
15:24:06.148 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
15:24:06.149 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,36] - 开始导出简单Word文档，标题: 简单Word文档
15:24:06.621 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 简单Word文档导出成功，大小: 2904 bytes
15:24:06.629 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,62] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_152406.docx, 大小: 2904 bytes
17:35:47.629 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
17:35:47.634 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
17:36:35.566 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 416314 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
17:36:35.568 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:36:36.399 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
17:36:36.399 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:36:36.399 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:36:36.460 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:36:36.956 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
17:36:37.194 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
17:36:37.485 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
17:36:37.486 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
17:36:37.777 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
17:36:37.801 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
17:36:37.802 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
17:36:37.876 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
17:36:37.880 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
17:36:37.882 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
17:36:37.883 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
17:36:37.883 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
17:36:37.883 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
17:36:37.883 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
17:36:37.884 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
17:36:37.885 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
17:36:37.887 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
17:36:37.930 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
17:36:37.946 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.777 seconds (JVM running for 3.149)
17:36:41.926 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:45:37.332 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,109] - 验证简单Word导出请求，标题: 简单Word文档
17:45:37.333 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,145] - 简单Word导出请求验证通过
17:45:37.436 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
17:45:37.437 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,36] - 开始导出简单Word文档，标题: 简单Word文档
17:45:37.866 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 简单Word文档导出成功，大小: 2882 bytes
17:45:37.871 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,62] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_174537.docx, 大小: 2882 bytes
18:02:13.175 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:02:13.178 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:02:18.366 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 448518 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
18:02:18.369 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:02:19.264 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
18:02:19.264 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:02:19.265 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:02:19.312 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:02:19.817 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:02:20.037 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:02:20.345 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:02:20.347 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:02:20.706 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:02:20.730 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:02:20.731 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:02:20.806 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:02:20.811 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:02:20.814 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:02:20.814 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:02:20.815 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:02:20.815 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:02:20.815 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:02:20.815 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:02:20.816 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:02:20.817 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:02:20.861 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
18:02:20.879 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.927 seconds (JVM running for 3.359)
18:02:23.457 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:03:03.008 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,109] - 验证简单Word导出请求，标题: 简单Word文档
18:03:03.008 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,145] - 简单Word导出请求验证通过
18:03:03.591 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:03:03.591 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,39] - 开始导出简单Word文档，标题: 简单Word文档
18:03:03.856 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,53] - 使用JSON结构化数据导出，节点数量: 1
18:03:03.857 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,81] - 开始解析JSON结构化内容，节点数量: 1
18:03:03.858 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - JSON结构化内容解析完成
18:03:03.910 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,68] - 简单Word文档导出成功，大小: 2786 bytes
18:03:03.916 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,62] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_180303.docx, 大小: 2786 bytes
18:09:56.922 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,109] - 验证简单Word导出请求，标题: 简单Word文档
18:09:57.209 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:12:11.848 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:12:11.851 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:12:44.541 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 461996 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
18:12:44.543 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:12:45.316 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
18:12:45.317 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:12:45.317 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:12:45.359 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:12:45.828 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:12:46.033 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:12:46.310 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:12:46.311 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:12:46.602 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:12:46.625 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:12:46.626 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:12:46.693 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:12:46.698 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:12:46.700 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:12:46.701 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:12:46.701 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:12:46.701 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:12:46.701 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:12:46.701 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:12:46.702 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:12:46.703 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:12:46.740 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
18:12:46.753 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.54 seconds (JVM running for 2.869)
18:12:47.273 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:12:53.781 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,102] - 验证简单Word导出请求，标题: 简单Word文档
18:12:53.784 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,134] - 简单Word导出请求验证通过
18:12:54.203 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:12:54.204 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,39] - 开始导出简单Word文档，标题: 简单Word文档
18:12:54.458 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,53] - 使用JSON结构化数据导出，节点数量: 1
18:12:54.459 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,59] - 文档包含图片数量: 1
18:12:54.459 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,88] - 开始解析JSON结构化内容，节点数量: 1
18:12:54.460 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,94] - JSON结构化内容解析完成
18:12:54.515 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,75] - 简单Word文档导出成功，大小: 2787 bytes
18:12:54.524 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,55] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_181254.docx, 大小: 2787 bytes
18:18:17.215 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:18:17.218 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:18:21.700 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 469195 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
18:18:21.703 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:18:22.564 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
18:18:22.565 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:18:22.565 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:18:22.615 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:18:23.142 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:18:23.370 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:18:23.667 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:18:23.668 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:18:23.997 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:18:24.021 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:18:24.022 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:18:24.100 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:18:24.104 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:18:24.106 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:18:24.107 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:18:24.107 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:18:24.107 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:18:24.107 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:18:24.107 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:18:24.108 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:18:24.109 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:18:24.150 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
18:18:24.164 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.839 seconds (JVM running for 3.175)
18:18:27.321 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:18:30.412 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
18:18:30.427 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 2, 图片数量: 1, 文本节点: 1
18:18:30.427 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
18:18:30.803 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:18:30.814 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 2, 图片数量: 1, 文本节点: 1
18:18:30.815 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,39] - 开始导出简单Word文档，标题: 简单Word文档
18:18:31.064 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,53] - 使用JSON结构化数据导出，节点数量: 1
18:18:31.065 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,59] - 文档包含图片数量: 1
18:18:31.065 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,88] - 开始解析JSON结构化内容，节点数量: 1
18:18:31.067 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,94] - JSON结构化内容解析完成
18:18:31.126 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,75] - 简单Word文档导出成功，大小: 2786 bytes
18:18:31.135 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_181831.docx, 大小: 2786 bytes
18:20:42.355 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
18:20:42.355 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 2, 图片数量: 1, 文本节点: 1
18:20:42.356 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
18:20:42.366 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:20:42.367 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 2, 图片数量: 1, 文本节点: 1
18:20:42.367 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,39] - 开始导出简单Word文档，标题: 简单Word文档
18:20:42.371 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,53] - 使用JSON结构化数据导出，节点数量: 1
18:20:42.372 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,59] - 文档包含图片数量: 1
18:20:42.372 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,88] - 开始解析JSON结构化内容，节点数量: 1
18:20:42.372 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,94] - JSON结构化内容解析完成
18:20:42.379 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,75] - 简单Word文档导出成功，大小: 2786 bytes
18:20:42.382 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_182042.docx, 大小: 2786 bytes
18:25:02.266 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:25:02.268 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:25:06.665 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 477888 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
18:25:06.667 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:25:07.474 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
18:25:07.475 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:25:07.475 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:25:07.519 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:25:08.001 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:25:08.234 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:25:08.514 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:25:08.515 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:25:08.836 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:25:08.862 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:25:08.863 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:25:08.933 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:25:08.937 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:25:08.940 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:25:08.940 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:25:08.940 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:25:08.941 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:25:08.941 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:25:08.941 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:25:08.941 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:25:08.943 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:25:08.980 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
18:25:08.995 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.694 seconds (JVM running for 3.065)
18:25:09.799 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:25:23.175 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
18:25:23.176 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 2, 图片数量: 1, 文本节点: 1
18:25:23.176 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
18:25:23.187 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:25:23.187 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 2, 图片数量: 1, 文本节点: 1
18:25:23.187 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,39] - 开始导出简单Word文档，标题: 简单Word文档
18:25:23.452 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,53] - 使用JSON结构化数据导出，节点数量: 1
18:25:23.453 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,59] - 文档包含图片数量: 1
18:25:23.453 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,88] - 开始解析JSON结构化内容，节点数量: 1
18:25:23.456 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,220] - 开始处理图片 - 大小: 2.0KB, 尺寸: autoxauto, 格式: PNG
18:25:23.457 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,328] - 图片解码成功，大小: 2003 bytes (2.0KB)
18:25:23.458 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,372] - 插入图片，尺寸: 400x300
18:25:23.512 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,378] - 图片插入成功 - 格式: PNG, 尺寸: 400x300
18:25:23.513 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,225] - 图片处理完成 - 大小: 2.0KB, 尺寸: autoxauto, 格式: PNG
18:25:23.513 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,94] - JSON结构化内容解析完成
18:25:23.564 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,75] - 简单Word文档导出成功，大小: 5288 bytes
18:25:23.572 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_182523.docx, 大小: 5288 bytes
18:27:43.124 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
18:27:43.125 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 10, 图片数量: 1, 文本节点: 3
18:27:43.125 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
18:27:43.139 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:27:43.139 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 10, 图片数量: 1, 文本节点: 3
18:27:43.139 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,39] - 开始导出简单Word文档，标题: 简单Word文档
18:27:43.144 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,53] - 使用JSON结构化数据导出，节点数量: 6
18:27:43.144 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,59] - 文档包含图片数量: 1
18:27:43.145 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,88] - 开始解析JSON结构化内容，节点数量: 6
18:27:43.145 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,220] - 开始处理图片 - 大小: 2.0KB, 尺寸: 44pxx36.1429px, 格式: PNG
18:27:43.146 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,328] - 图片解码成功，大小: 2003 bytes (2.0KB)
18:27:43.147 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,372] - 插入图片，尺寸: 44x300
18:27:43.151 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,378] - 图片插入成功 - 格式: PNG, 尺寸: 44x300
18:27:43.151 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,225] - 图片处理完成 - 大小: 2.0KB, 尺寸: 44pxx36.1429px, 格式: PNG
18:27:43.152 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,94] - JSON结构化内容解析完成
18:27:43.164 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,75] - 简单Word文档导出成功，大小: 5332 bytes
18:27:43.171 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_182743.docx, 大小: 5332 bytes
18:28:28.583 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:28:28.586 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:28:32.343 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 483579 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
18:28:32.344 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:28:33.157 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
18:28:33.158 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:28:33.158 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:28:33.205 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:28:33.676 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:28:33.883 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:28:34.169 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:28:34.170 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:28:34.477 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:28:34.500 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:28:34.501 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:28:34.570 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:28:34.574 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:28:34.577 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:28:34.577 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:28:34.578 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:28:34.578 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:28:34.578 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:28:34.578 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:28:34.579 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:28:34.580 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:28:34.620 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
18:28:34.634 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.608 seconds (JVM running for 2.977)
18:28:34.896 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:28:38.123 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
18:28:38.124 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 10, 图片数量: 1, 文本节点: 3
18:28:38.124 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
18:28:38.149 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:28:38.150 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 10, 图片数量: 1, 文本节点: 3
18:28:38.150 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,40] - 开始导出简单Word文档，标题: 简单Word文档
18:28:38.399 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,54] - 使用JSON结构化数据导出，节点数量: 6
18:28:38.400 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [fixJsonStructure,655] - JSON结构修复完成 - 原始节点数: 6, 修复后节点数: 7
18:28:38.400 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,58] - JSON结构修复完成，修复后节点数量: 7
18:28:38.401 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,64] - 文档包含图片数量: 1
18:28:38.401 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - 开始解析JSON结构化内容，节点数量: 7
18:28:38.403 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,225] - 开始处理图片 - 大小: 2.0KB, 尺寸: 44pxx36.1429px, 格式: PNG
18:28:38.403 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,333] - 图片解码成功，大小: 2003 bytes (2.0KB)
18:28:38.404 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,377] - 插入图片，尺寸: 44x300
18:28:38.451 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,383] - 图片插入成功 - 格式: PNG, 尺寸: 44x300
18:28:38.451 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,230] - 图片处理完成 - 大小: 2.0KB, 尺寸: 44pxx36.1429px, 格式: PNG
18:28:38.452 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,99] - JSON结构化内容解析完成
18:28:38.500 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,80] - 简单Word文档导出成功，大小: 5331 bytes
18:28:38.506 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_182838.docx, 大小: 5331 bytes
18:36:52.258 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:36:52.262 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:36:56.410 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 494852 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
18:36:56.412 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:36:57.261 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
18:36:57.261 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:36:57.262 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:36:57.309 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:36:57.825 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:36:58.040 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:36:58.331 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:36:58.332 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:36:58.640 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:36:58.664 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:36:58.664 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:36:58.735 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:36:58.739 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:36:58.741 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:36:58.742 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:36:58.742 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:36:58.742 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:36:58.742 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:36:58.742 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:36:58.743 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:36:58.744 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:36:58.782 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
18:36:58.797 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.758 seconds (JVM running for 3.083)
18:36:58.905 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:37:13.035 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
18:37:13.036 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 4, 图片数量: 1, 文本节点: 1
18:37:13.037 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
18:37:13.048 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:37:13.049 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 4, 图片数量: 1, 文本节点: 1
18:37:13.049 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,40] - 开始导出简单Word文档，标题: 简单Word文档
18:37:13.306 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,54] - 使用JSON结构化数据导出，节点数量: 2
18:37:13.307 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [fixJsonStructure,637] - JSON结构修复完成 - 原始节点数: 2, 修复后节点数: 3
18:37:13.307 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,58] - JSON结构修复完成，修复后节点数量: 3
18:37:13.308 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,64] - 文档包含图片数量: 1
18:37:13.308 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - 开始解析JSON结构化内容，节点数量: 3
18:37:13.310 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,225] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:37:13.310 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,348] - 图片解码成功，大小: 2003 bytes (2.0KB)
18:37:13.310 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,359] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
18:37:13.362 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,365] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
18:37:13.362 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,230] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:37:13.362 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,99] - JSON结构化内容解析完成
18:37:13.416 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,80] - 简单Word文档导出成功，大小: 5287 bytes
18:37:13.426 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_183713.docx, 大小: 5287 bytes
18:38:04.493 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
18:38:04.509 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 4, 图片数量: 2, 文本节点: 2
18:38:04.510 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
18:38:04.872 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:38:04.891 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 4, 图片数量: 2, 文本节点: 2
18:38:04.892 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,40] - 开始导出简单Word文档，标题: 简单Word文档
18:38:04.896 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,54] - 使用JSON结构化数据导出，节点数量: 2
18:38:04.897 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [fixJsonStructure,637] - JSON结构修复完成 - 原始节点数: 2, 修复后节点数: 4
18:38:04.897 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,58] - JSON结构修复完成，修复后节点数量: 4
18:38:04.897 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,64] - 文档包含图片数量: 2
18:38:04.897 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - 开始解析JSON结构化内容，节点数量: 4
18:38:04.898 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,225] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:38:04.898 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,348] - 图片解码成功，大小: 2003 bytes (2.0KB)
18:38:04.898 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,359] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
18:38:04.901 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,365] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
18:38:04.901 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,230] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:38:04.911 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,225] - 开始处理图片 - 大小: 3.7MB, 显示尺寸: 282pxx279px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
18:38:04.937 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,348] - 图片解码成功，大小: 3858851 bytes (3.7MB)
18:38:04.939 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,359] - 图片尺寸计算完成 - 宽度: 282px, 高度: 279px, 来源: CSS样式
18:38:04.952 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,365] - 图片插入成功 - 格式: PNG, 尺寸: 282x279
18:38:04.952 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,230] - 图片处理完成 - 大小: 3.7MB, 显示尺寸: 282pxx279px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
18:38:04.952 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,99] - JSON结构化内容解析完成
18:38:05.072 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,80] - 简单Word文档导出成功，大小: 3862356 bytes
18:38:05.178 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_183805.docx, 大小: 3862356 bytes
18:38:21.134 [http-nio-9550-exec-2] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
18:38:21.144 [http-nio-9550-exec-2] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 4, 图片数量: 2, 文本节点: 2
18:38:21.145 [http-nio-9550-exec-2] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
18:38:21.417 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:38:21.428 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 4, 图片数量: 2, 文本节点: 2
18:38:21.429 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,40] - 开始导出简单Word文档，标题: 简单Word文档
18:38:21.443 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,54] - 使用JSON结构化数据导出，节点数量: 2
18:38:21.443 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [fixJsonStructure,637] - JSON结构修复完成 - 原始节点数: 2, 修复后节点数: 4
18:38:21.444 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,58] - JSON结构修复完成，修复后节点数量: 4
18:38:21.444 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,64] - 文档包含图片数量: 2
18:38:21.444 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - 开始解析JSON结构化内容，节点数量: 4
18:38:21.445 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,225] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:38:21.445 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,348] - 图片解码成功，大小: 2003 bytes (2.0KB)
18:38:21.445 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,359] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
18:38:21.449 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,365] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
18:38:21.449 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,230] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:38:21.456 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,225] - 开始处理图片 - 大小: 3.7MB, 显示尺寸: 282pxx279px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
18:38:21.475 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,348] - 图片解码成功，大小: 3858851 bytes (3.7MB)
18:38:21.476 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,359] - 图片尺寸计算完成 - 宽度: 282px, 高度: 279px, 来源: CSS样式
18:38:21.483 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,365] - 图片插入成功 - 格式: PNG, 尺寸: 282x279
18:38:21.484 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,230] - 图片处理完成 - 大小: 3.7MB, 显示尺寸: 282pxx279px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
18:38:21.484 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,99] - JSON结构化内容解析完成
18:38:21.588 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,80] - 简单Word文档导出成功，大小: 3862368 bytes
18:38:21.691 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_183821.docx, 大小: 3862368 bytes
18:45:51.854 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:45:51.858 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:45:55.910 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 506906 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
18:45:55.912 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:45:56.753 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
18:45:56.754 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:45:56.755 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:45:56.800 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:45:57.279 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:45:57.497 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:45:57.784 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:45:57.785 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:45:58.129 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:45:58.154 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:45:58.155 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:45:58.229 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:45:58.233 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:45:58.235 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:45:58.236 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:45:58.236 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:45:58.236 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:45:58.236 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:45:58.236 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:45:58.237 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:45:58.239 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:45:58.281 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
18:45:58.297 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.744 seconds (JVM running for 3.067)
18:45:59.375 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:53:35.824 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:53:35.827 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:53:40.219 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 516817 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
18:53:40.221 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:53:41.022 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
18:53:41.022 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:53:41.022 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:53:41.067 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:53:41.545 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:53:41.759 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:53:42.065 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:53:42.066 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:53:42.448 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:53:42.475 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:53:42.477 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:53:42.554 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:53:42.558 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:53:42.560 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:53:42.561 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:53:42.561 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:53:42.561 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:53:42.561 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:53:42.561 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:53:42.562 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:53:42.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:53:42.601 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
18:53:42.617 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.733 seconds (JVM running for 3.073)
18:53:43.347 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:56:01.776 [http-nio-9550-exec-2] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
18:56:01.786 [http-nio-9550-exec-2] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 4, 图片数量: 2, 文本节点: 2
18:56:01.787 [http-nio-9550-exec-2] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
18:56:02.047 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:56:02.059 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 4, 图片数量: 2, 文本节点: 2
18:56:02.059 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
18:56:02.313 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 3
18:56:02.315 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 2
18:56:02.315 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 3
18:56:02.317 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processInlineImage,214] - 开始处理内联图片 - 大小: 2.0KB, 显示尺寸: 50pxx50px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:56:02.365 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processInlineBase64Image,273] - 内联图片插入成功 - 格式: PNG, 尺寸: 24x24
18:56:02.366 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processInlineImage,218] - 内联图片处理完成 - 大小: 2.0KB, 显示尺寸: 50pxx50px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:56:02.374 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,393] - 开始处理图片 - 大小: 3.7MB, 显示尺寸: 117pxx118px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
18:56:02.410 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,516] - 图片解码成功，大小: 3858851 bytes (3.7MB)
18:56:02.411 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,527] - 图片尺寸计算完成 - 宽度: 117px, 高度: 118px, 来源: CSS样式
18:56:02.423 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,533] - 图片插入成功 - 格式: PNG, 尺寸: 117x118
18:56:02.423 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,398] - 图片处理完成 - 大小: 3.7MB, 显示尺寸: 117pxx118px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
18:56:02.424 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
18:56:02.577 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 3862361 bytes
18:56:02.681 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_185602.docx, 大小: 3862361 bytes
18:57:39.690 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
18:57:39.691 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 3, 图片数量: 1, 文本节点: 2
18:57:39.692 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
18:57:39.704 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
18:57:39.705 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 3, 图片数量: 1, 文本节点: 2
18:57:39.705 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
18:57:39.711 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 2
18:57:39.712 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 1
18:57:39.712 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 2
18:57:39.713 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processInlineImage,214] - 开始处理内联图片 - 大小: 2.0KB, 显示尺寸: 50pxx50px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:57:39.715 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processInlineBase64Image,273] - 内联图片插入成功 - 格式: PNG, 尺寸: 24x24
18:57:39.716 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processInlineImage,218] - 内联图片处理完成 - 大小: 2.0KB, 显示尺寸: 50pxx50px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:57:39.717 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
18:57:39.728 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 5302 bytes
18:57:39.735 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_185739.docx, 大小: 5302 bytes
19:06:59.767 [http-nio-9550-exec-4] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
19:06:59.768 [http-nio-9550-exec-4] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
19:06:59.768 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: null
19:06:59.773 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
19:06:59.774 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 3, 总列数: 8
19:06:59.806 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
19:06:59.836 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 260px (3900twips)
19:06:59.837 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.脱漆
2.冲洗__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
19:06:59.839 [http-nio-9550-exec-4] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
19:07:00.112 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
19:07:00.113 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
19:07:00.117 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
19:07:00.118 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
19:07:00.118 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
19:07:00.118 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
19:07:00.119 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
19:07:00.120 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
19:07:00.121 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
19:07:00.121 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
19:07:00.129 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3004 bytes
19:07:00.134 [http-nio-9550-exec-4] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250824_190700.docx, 大小: 3004 bytes
19:09:01.231 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
19:09:01.242 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 7, 图片数量: 2, 文本节点: 3
19:09:01.242 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
19:09:01.549 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
19:09:01.562 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 7, 图片数量: 2, 文本节点: 3
19:09:01.563 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
19:09:01.565 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 6
19:09:01.565 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 2
19:09:01.566 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 6
19:09:01.566 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,393] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:09:01.566 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,516] - 图片解码成功，大小: 2003 bytes (2.0KB)
19:09:01.567 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,527] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
19:09:01.569 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,533] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
19:09:01.570 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,398] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:09:01.577 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,393] - 开始处理图片 - 大小: 3.7MB, 显示尺寸: 149pxx142px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
19:09:01.599 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,516] - 图片解码成功，大小: 3858851 bytes (3.7MB)
19:09:01.600 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,527] - 图片尺寸计算完成 - 宽度: 149px, 高度: 142px, 来源: CSS样式
19:09:01.608 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,533] - 图片插入成功 - 格式: PNG, 尺寸: 149x142
19:09:01.608 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,398] - 图片处理完成 - 大小: 3.7MB, 显示尺寸: 149pxx142px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
19:09:01.608 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
19:09:01.718 [http-nio-9550-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 3862360 bytes
19:09:01.819 [http-nio-9550-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_190901.docx, 大小: 3862360 bytes
19:13:19.485 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:13:19.488 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:13:24.239 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 541933 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
19:13:24.241 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:13:25.142 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
19:13:25.143 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:13:25.143 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:13:25.192 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:13:25.721 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:13:25.943 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:13:26.258 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:13:26.259 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:13:26.595 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:13:26.621 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:13:26.622 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:13:26.693 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:13:26.700 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:13:26.703 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:13:26.703 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:13:26.703 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:13:26.703 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:13:26.704 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:13:26.704 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:13:26.704 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:13:26.706 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:13:26.746 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
19:13:26.762 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.89 seconds (JVM running for 3.248)
19:13:29.983 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:13:32.073 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
19:13:32.074 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 2, 图片数量: 1, 文本节点: 1
19:13:32.074 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
19:13:32.102 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
19:13:32.102 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 2, 图片数量: 1, 文本节点: 1
19:13:32.103 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
19:13:32.392 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 2
19:13:32.394 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 1
19:13:32.394 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 2
19:13:32.396 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,518] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:13:32.396 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,641] - 图片解码成功，大小: 2003 bytes (2.0KB)
19:13:32.398 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,652] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
19:13:32.453 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,658] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
19:13:32.454 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,523] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:13:32.454 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
19:13:32.512 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 5255 bytes
19:13:32.519 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_191332.docx, 大小: 5255 bytes
19:14:15.606 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
19:14:15.607 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 3, 图片数量: 1, 文本节点: 2
19:14:15.607 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
19:14:15.620 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
19:14:15.621 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 3, 图片数量: 1, 文本节点: 2
19:14:15.621 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
19:14:15.624 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 3
19:14:15.625 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 1
19:14:15.625 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 3
19:14:15.626 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,518] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:14:15.626 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,641] - 图片解码成功，大小: 2003 bytes (2.0KB)
19:14:15.626 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,652] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
19:14:15.628 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,658] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
19:14:15.629 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,523] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:14:15.629 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
19:14:15.635 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 5263 bytes
19:14:15.638 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_191415.docx, 大小: 5263 bytes
19:15:16.998 [http-nio-9550-exec-2] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
19:15:16.999 [http-nio-9550-exec-2] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 7, 图片数量: 3, 文本节点: 4
19:15:16.999 [http-nio-9550-exec-2] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
19:15:17.014 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
19:15:17.015 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 7, 图片数量: 3, 文本节点: 4
19:15:17.015 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
19:15:17.022 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 7
19:15:17.023 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 3
19:15:17.023 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 7
19:15:17.023 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,518] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:15:17.024 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,641] - 图片解码成功，大小: 2003 bytes (2.0KB)
19:15:17.024 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,652] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
19:15:17.027 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,658] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
19:15:17.027 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,523] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:15:17.028 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,518] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:15:17.028 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,641] - 图片解码成功，大小: 2003 bytes (2.0KB)
19:15:17.030 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,652] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
19:15:17.034 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,658] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
19:15:17.034 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,523] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:15:17.035 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,518] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:15:17.035 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,641] - 图片解码成功，大小: 2003 bytes (2.0KB)
19:15:17.035 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,652] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
19:15:17.037 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,658] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
19:15:17.037 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,523] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:15:17.037 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
19:15:17.044 [http-nio-9550-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 5328 bytes
19:15:17.048 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_191517.docx, 大小: 5328 bytes
19:19:37.193 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:19:37.196 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:19:42.231 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 550512 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
19:19:42.234 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:19:43.164 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
19:19:43.165 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:19:43.165 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:19:43.214 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:19:43.770 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:19:44.022 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:19:44.353 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:19:44.354 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:19:44.707 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:19:44.735 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:19:44.737 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:19:44.824 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:19:44.830 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:19:44.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:19:44.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:19:44.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:19:44.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:19:44.834 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:19:44.834 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:19:44.834 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:19:44.836 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:19:44.881 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
19:19:44.899 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.046 seconds (JVM running for 3.453)
19:19:47.456 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:20:46.975 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
19:20:46.976 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 5, 图片数量: 2, 文本节点: 3
19:20:46.977 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
19:20:47.000 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
19:20:47.001 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 5, 图片数量: 2, 文本节点: 3
19:20:47.002 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
19:20:47.337 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 4
19:20:47.338 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 2
19:20:47.339 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 4
19:20:47.340 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,222] - 开始处理同行内联图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:20:47.390 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processInlineBase64ImageInSameParagraph,281] - 同行内联图片插入成功 - 格式: PNG, 尺寸: 24x24
19:20:47.391 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,226] - 同行内联图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:20:47.391 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,605] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:20:47.392 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,728] - 图片解码成功，大小: 2003 bytes (2.0KB)
19:20:47.392 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,739] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
19:20:47.394 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,745] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
19:20:47.395 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,610] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:20:47.396 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
19:20:47.447 [http-nio-9550-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 5357 bytes
19:20:47.455 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_192047.docx, 大小: 5357 bytes
19:24:21.147 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:24:21.150 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:24:26.517 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 556728 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
19:24:26.520 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:24:27.405 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
19:24:27.405 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:24:27.406 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:24:27.450 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:24:28.009 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:24:28.250 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:24:28.573 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:24:28.574 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:24:28.890 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:24:28.918 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:24:28.919 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:24:29.004 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:24:29.010 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:24:29.012 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:24:29.013 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:24:29.013 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:24:29.013 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:24:29.014 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:24:29.014 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:24:29.014 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:24:29.016 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:24:29.058 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
19:24:29.088 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.939 seconds (JVM running for 3.305)
19:24:31.211 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:24:42.261 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
19:24:42.262 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 2, 图片数量: 1, 文本节点: 1
19:24:42.262 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
19:24:42.275 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
19:24:42.276 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 2, 图片数量: 1, 文本节点: 1
19:24:42.276 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
19:24:42.603 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 1
19:24:42.604 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 1
19:24:42.605 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 1
19:24:42.608 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,370] - 开始处理同行内联图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:24:42.668 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processInlineBase64ImageInSameParagraph,429] - 同行内联图片插入成功 - 格式: PNG, 尺寸: 24x24
19:24:42.669 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,374] - 同行内联图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:24:42.669 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
19:24:42.718 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 5291 bytes
19:24:42.727 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_192442.docx, 大小: 5291 bytes
19:24:58.604 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
19:24:58.604 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 5, 图片数量: 2, 文本节点: 3
19:24:58.605 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
19:24:58.617 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
19:24:58.618 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 5, 图片数量: 2, 文本节点: 3
19:24:58.619 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
19:24:58.623 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 4
19:24:58.624 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 2
19:24:58.624 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 4
19:24:58.625 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,370] - 开始处理同行内联图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:24:58.629 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processInlineBase64ImageInSameParagraph,429] - 同行内联图片插入成功 - 格式: PNG, 尺寸: 24x24
19:24:58.630 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,374] - 同行内联图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:24:58.631 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,753] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:24:58.631 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,876] - 图片解码成功，大小: 2003 bytes (2.0KB)
19:24:58.632 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,887] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
19:24:58.634 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,893] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
19:24:58.634 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,758] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:24:58.635 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
19:24:58.644 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 5360 bytes
19:24:58.651 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_192458.docx, 大小: 5360 bytes
19:25:34.914 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
19:25:34.915 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 6, 图片数量: 2, 文本节点: 4
19:25:34.916 [http-nio-9550-exec-9] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
19:25:34.936 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
19:25:34.937 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 6, 图片数量: 2, 文本节点: 4
19:25:34.937 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
19:25:34.942 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 5
19:25:34.943 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 2
19:25:34.943 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 5
19:25:34.945 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,370] - 开始处理同行内联图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:25:34.949 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processInlineBase64ImageInSameParagraph,429] - 同行内联图片插入成功 - 格式: PNG, 尺寸: 24x24
19:25:34.950 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,374] - 同行内联图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:25:34.952 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,753] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:25:34.952 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,876] - 图片解码成功，大小: 2003 bytes (2.0KB)
19:25:34.953 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,887] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
19:25:34.955 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,893] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
19:25:34.955 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,758] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
19:25:34.957 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
19:25:34.965 [http-nio-9550-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 5395 bytes
19:25:34.971 [http-nio-9550-exec-10] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_192534.docx, 大小: 5395 bytes
21:13:57.139 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
21:13:57.151 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 7, 图片数量: 3, 文本节点: 4
21:13:57.152 [http-nio-9550-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
21:13:57.256 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
21:13:57.269 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 7, 图片数量: 3, 文本节点: 4
21:13:57.270 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
21:13:57.273 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 6
21:13:57.274 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 3
21:13:57.274 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 6
21:13:57.275 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,370] - 开始处理同行内联图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:13:57.277 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processInlineBase64ImageInSameParagraph,429] - 同行内联图片插入成功 - 格式: PNG, 尺寸: 24x24
21:13:57.277 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,374] - 同行内联图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:13:57.283 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,753] - 开始处理图片 - 大小: 3.7MB, 显示尺寸: 248.261pxx462px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
21:13:57.304 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,876] - 图片解码成功，大小: 3858851 bytes (3.7MB)
21:13:57.305 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,887] - 图片尺寸计算完成 - 宽度: 400px, 高度: 462px, 来源: CSS样式
21:13:57.313 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,893] - 图片插入成功 - 格式: PNG, 尺寸: 400x462
21:13:57.314 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,758] - 图片处理完成 - 大小: 3.7MB, 显示尺寸: 248.261pxx462px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
21:13:57.315 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,753] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:13:57.315 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,876] - 图片解码成功，大小: 2003 bytes (2.0KB)
21:13:57.315 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,887] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式
21:13:57.319 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,893] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
21:13:57.320 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,758] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:13:57.321 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
21:13:57.440 [http-nio-9550-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 3862450 bytes
21:13:57.537 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_211357.docx, 大小: 3862450 bytes
21:23:04.667 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
21:23:04.673 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
21:23:10.954 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 703230 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
21:23:10.957 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
21:23:11.850 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
21:23:11.851 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
21:23:11.851 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
21:23:11.900 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
21:23:12.434 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
21:23:12.686 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
21:23:13.000 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
21:23:13.002 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
21:23:13.338 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
21:23:13.371 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
21:23:13.373 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
21:23:13.456 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
21:23:13.461 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
21:23:13.465 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
21:23:13.466 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
21:23:13.467 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
21:23:13.467 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
21:23:13.467 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
21:23:13.467 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
21:23:13.468 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
21:23:13.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
21:23:13.518 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
21:23:13.534 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.963 seconds (JVM running for 3.359)
21:23:15.384 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:23:22.734 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
21:23:22.746 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 15, 图片数量: 3, 文本节点: 4
21:23:22.747 [http-nio-9550-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
21:23:22.846 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
21:23:22.857 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 15, 图片数量: 3, 文本节点: 4
21:23:22.857 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
21:23:23.233 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 14
21:23:23.235 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 3
21:23:23.235 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 14
21:23:23.238 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,370] - 开始处理同行内联图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:23:23.303 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processInlineBase64ImageInSameParagraph,429] - 同行内联图片插入成功 - 格式: PNG, 尺寸: 24x24
21:23:23.304 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,374] - 同行内联图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:23:23.314 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,753] - 开始处理图片 - 大小: 3.7MB, 显示尺寸: 248.261pxx462px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
21:23:23.349 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,876] - 图片解码成功，大小: 3858851 bytes (3.7MB)
21:23:23.351 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,887] - 图片尺寸计算完成 - 宽度: 263px, 高度: 462px, 来源: CSS样式(高度) + 宽高比
21:23:23.362 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,893] - 图片插入成功 - 格式: PNG, 尺寸: 263x462
21:23:23.363 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,758] - 图片处理完成 - 大小: 3.7MB, 显示尺寸: 248.261pxx462px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
21:23:23.363 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,753] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:23:23.364 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,876] - 图片解码成功，大小: 2003 bytes (2.0KB)
21:23:23.364 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,887] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
21:23:23.367 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,893] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
21:23:23.367 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,758] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:23:23.369 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
21:23:23.521 [http-nio-9550-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 3862452 bytes
21:23:23.624 [http-nio-9550-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_212323.docx, 大小: 3862452 bytes
21:25:06.729 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
21:25:06.730 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 4, 图片数量: 1, 文本节点: 3
21:25:06.730 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
21:25:06.738 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
21:25:06.738 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 4, 图片数量: 1, 文本节点: 3
21:25:06.739 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
21:25:06.742 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 3
21:25:06.743 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 1
21:25:06.743 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 3
21:25:06.743 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,370] - 开始处理同行内联图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:25:06.746 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processInlineBase64ImageInSameParagraph,429] - 同行内联图片插入成功 - 格式: PNG, 尺寸: 24x24
21:25:06.747 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,374] - 同行内联图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:25:06.748 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
21:25:06.755 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 5340 bytes
21:25:06.759 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_212506.docx, 大小: 5340 bytes
21:26:34.942 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,119] - 验证简单Word导出请求，标题: 简单Word文档
21:26:34.953 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,137] - JSON内容验证通过 - 节点总数: 5, 图片数量: 2, 文本节点: 3
21:26:34.953 [http-nio-9550-exec-6] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,167] - 简单Word导出请求验证通过
21:26:35.273 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,41] - 接收到简单Word导出请求，标题: 简单Word文档
21:26:35.286 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,47] - JSON内容统计 - 总节点: 5, 图片数量: 2, 文本节点: 3
21:26:35.286 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,41] - 开始导出简单Word文档，标题: 简单Word文档
21:26:35.288 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,55] - 使用JSON结构化数据导出，节点数量: 4
21:26:35.289 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,61] - 文档包含图片数量: 2
21:26:35.289 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,91] - 开始解析JSON结构化内容，节点数量: 4
21:26:35.290 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,370] - 开始处理同行内联图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:26:35.292 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processInlineBase64ImageInSameParagraph,429] - 同行内联图片插入成功 - 格式: PNG, 尺寸: 24x24
21:26:35.293 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processInlineImageInSameParagraph,374] - 同行内联图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
21:26:35.299 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,753] - 开始处理图片 - 大小: 3.7MB, 显示尺寸: 264.958pxx493px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
21:26:35.320 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,876] - 图片解码成功，大小: 3858851 bytes (3.7MB)
21:26:35.321 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,887] - 图片尺寸计算完成 - 宽度: 280px, 高度: 493px, 来源: CSS样式(高度) + 宽高比
21:26:35.327 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,893] - 图片插入成功 - 格式: PNG, 尺寸: 280x493
21:26:35.327 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,758] - 图片处理完成 - 大小: 3.7MB, 显示尺寸: 264.958pxx493px, 原始尺寸: 1024x1800, 宽高比: 0.5689, 格式: PNG
21:26:35.328 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,97] - JSON结构化内容解析完成
21:26:35.427 [http-nio-9550-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,78] - 简单Word文档导出成功，大小: 3862406 bytes
21:26:35.541 [http-nio-9550-exec-7] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,72] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250824_212635.docx, 大小: 3862406 bytes
22:27:09.441 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 781719 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
22:27:09.443 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:27:10.234 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
22:27:10.234 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:27:10.234 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:27:10.276 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:27:10.744 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:27:10.973 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:27:11.265 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
22:27:11.266 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
22:27:11.554 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
22:27:11.581 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
22:27:11.582 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
22:27:11.655 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
22:27:11.661 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
22:27:11.663 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
22:27:11.664 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
22:27:11.664 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
22:27:11.664 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
22:27:11.664 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
22:27:11.664 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
22:27:11.665 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
22:27:11.666 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
22:27:11.710 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
22:27:11.826 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
22:27:11.828 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
22:27:11.832 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9550"]
22:27:11.832 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
22:27:11.835 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9550"]
22:27:11.836 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9550"]
22:27:17.399 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
22:27:17.406 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
22:27:19.474 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 782142 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
22:27:19.479 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:27:20.966 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
22:27:20.966 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:27:20.967 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:27:21.002 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:27:21.515 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:27:21.714 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:27:22.026 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
22:27:22.027 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
22:27:22.377 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
22:27:22.399 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
22:27:22.400 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
22:27:22.516 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
22:27:22.526 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
22:27:22.531 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
22:27:22.532 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
22:27:22.533 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
22:27:22.533 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
22:27:22.534 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
22:27:22.535 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
22:27:22.536 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
22:27:22.540 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
22:27:22.634 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
22:27:22.685 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.83 seconds (JVM running for 4.259)
22:27:23.560 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
