-- 创建检验记录表设计表
CREATE TABLE `design_word` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `car_id` varchar(50) NOT NULL COMMENT '车辆ID',
  `title` varchar(200) DEFAULT '检验记录表' COMMENT '表格标题',
  `table_config` longtext COMMENT '表格配置信息(JSON格式)',
  `table_data` longtext COMMENT '表格数据(JSON格式)',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_car_id` (`car_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检验记录表设计表';

-- 插入示例数据
INSERT INTO `design_word` (`car_id`, `title`, `table_config`, `table_data`, `create_by`, `remark`) VALUES 
('0822', '检验记录表', 
'{
  "title": "检验记录表",
  "headers": [
    [
      "检查工序\\n名称",
      "检 查 项 目 及 技 术 条 件",
      "实 际 检 查 结 果",
      "完工",
      "",
      "操作员",
      "班组长",
      "检验员"
    ],
    [
      "",
      "",
      "",
      "月",
      "日",
      "",
      "",
      ""
    ]
  ],
  "cellRows": [],
  "merges": [],
  "headerMerges": [
    {
      "startRow": 0,
      "startCol": 0,
      "endRow": 1,
      "endCol": 0,
      "content": "检查工序\\n名称"
    },
    {
      "startRow": 0,
      "startCol": 1,
      "endRow": 1,
      "endCol": 1,
      "content": "检 查 项 目 及 技 术 条 件"
    },
    {
      "startRow": 0,
      "startCol": 2,
      "endRow": 1,
      "endCol": 2,
      "content": "实 际 检 查 结 果"
    },
    {
      "startRow": 0,
      "startCol": 3,
      "endRow": 0,
      "endCol": 4,
      "content": "完工"
    },
    {
      "startRow": 0,
      "startCol": 5,
      "endRow": 1,
      "endCol": 5,
      "content": "操作员"
    },
    {
      "startRow": 0,
      "startCol": 6,
      "endRow": 1,
      "endCol": 6,
      "content": "班组长"
    },
    {
      "startRow": 0,
      "startCol": 7,
      "endRow": 1,
      "endCol": 7,
      "content": "检验员"
    }
  ],
  "headerWidthConfig": {
    "columnWidths": [
      100,
      460,
      160,
      32,
      32,
      32,
      32,
      32
    ],
    "headerHeights": [
      35,
      35
    ]
  },
  "verticalHeadersConfig": [
    false,
    false,
    false,
    false,
    false,
    true,
    true,
    true
  ],
  "metadata": {
    "exportTime": "2025-08-24T04:46:09.128Z",
    "totalRows": 0,
    "totalColumns": 8,
    "headerRows": 2,
    "hasMergedCells": false,
    "hasHeaderMerges": true,
    "hasLatexProcessing": true,
    "useDynamicHeader": true,
    "hasCustomWidth": [
      100,
      460,
      160,
      32,
      32,
      32,
      32,
      32
    ],
    "hasVerticalHeaders": true
  }
}', 
'[]', 
'system', 
'示例检验记录表');
