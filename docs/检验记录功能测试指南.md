# 检验记录功能测试指南

## 功能概述

本功能实现了一个完整的检验记录表管理系统，包括：
- 检验记录数据查询
- 表格配置和数据插入
- 表格数据保存和修改
- 表格预览和导出

## 数据库准备

### 1. 创建数据表

执行以下SQL语句创建`design_word`表：

```sql
-- 创建检验记录表设计表
CREATE TABLE `design_word` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `car_id` varchar(50) NOT NULL COMMENT '车辆ID',
  `title` varchar(200) DEFAULT '检验记录表' COMMENT '表格标题',
  `table_config` longtext COMMENT '表格配置信息(JSON格式)',
  `table_data` longtext COMMENT '表格数据(JSON格式)',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_car_id` (`car_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检验记录表设计表';
```

### 2. 插入测试数据

```sql
-- 插入示例数据
INSERT INTO `design_word` (`car_id`, `title`, `table_config`, `table_data`, `create_by`, `remark`) VALUES 
('0822', '检验记录表', 
'{
  "title": "检验记录表",
  "headers": [
    [
      "检查工序\\n名称",
      "检 查 项 目 及 技 术 条 件",
      "实 际 检 查 结 果",
      "完工",
      "",
      "操作员",
      "班组长",
      "检验员"
    ],
    [
      "",
      "",
      "",
      "月",
      "日",
      "",
      "",
      ""
    ]
  ],
  "headerMerges": [
    {
      "startRow": 0,
      "startCol": 0,
      "endRow": 1,
      "endCol": 0,
      "content": "检查工序\\n名称"
    },
    {
      "startRow": 0,
      "startCol": 1,
      "endRow": 1,
      "endCol": 1,
      "content": "检 查 项 目 及 技 术 条 件"
    },
    {
      "startRow": 0,
      "startCol": 2,
      "endRow": 1,
      "endCol": 2,
      "content": "实 际 检 查 结 果"
    },
    {
      "startRow": 0,
      "startCol": 3,
      "endRow": 0,
      "endCol": 4,
      "content": "完工"
    },
    {
      "startRow": 0,
      "startCol": 5,
      "endRow": 1,
      "endCol": 5,
      "content": "操作员"
    },
    {
      "startRow": 0,
      "startCol": 6,
      "endRow": 1,
      "endCol": 6,
      "content": "班组长"
    },
    {
      "startRow": 0,
      "startCol": 7,
      "endRow": 1,
      "endCol": 7,
      "content": "检验员"
    }
  ],
  "headerWidthConfig": {
    "columnWidths": [100, 460, 160, 32, 32, 32, 32, 32],
    "headerHeights": [35, 35]
  },
  "verticalHeadersConfig": [false, false, false, false, false, true, true, true]
}', 
'[]', 
'system', 
'示例检验记录表');
```

## 后端测试

### 1. 启动后端服务

```bash
cd /home/<USER>/nl-mes/word-nl/logictrue-word
mvn spring-boot:run
```

### 2. 测试API接口

#### 检验记录数据查询接口
```bash
# 测试分页查询检验记录数据
curl -X GET "http://localhost:9550/word/getCheckRecord?pageNum=1&pageSize=10&carId=0822"
```

#### 表格设计管理接口
```bash
# 根据车辆ID查询表格设计
curl -X GET "http://localhost:9550/word/designWord/byCarId/0822"

# 保存表格设计
curl -X POST "http://localhost:9550/word/designWord/saveOrUpdate" \
  -H "Content-Type: application/json" \
  -d '{
    "carId": "0822",
    "title": "检验记录表",
    "tableConfig": "{}",
    "tableData": "[]",
    "status": 1
  }'
```

## 前端测试

### 1. 启动前端服务

```bash
cd /home/<USER>/nl-mes/word-nl/logictrue-ui-word
pnpm install
pnpm run dev
```

### 2. 功能测试步骤

#### 检验记录配置页面测试
1. 访问：`http://localhost:8080/check-record-config`
2. 在左侧输入车辆ID：`0822`
3. 点击"查询"按钮，验证数据加载
4. 点击记录行的"插入"按钮，验证数据插入到右侧表格
5. 在右侧表格中编辑数据
6. 点击"保存表格"按钮，验证数据保存
7. 点击"清空表格"按钮，验证表格清空

#### 检验记录预览页面测试
1. 访问：`http://localhost:8080/check-record-preview`
2. 输入车辆ID：`0822`
3. 点击"查询"按钮，验证表格数据加载和渲染
4. 验证表格配置正确应用（表头合并、列宽等）
5. 点击"导出Word"按钮，验证Word文档导出功能
6. 点击"刷新"按钮，验证数据重新加载

## 测试用例

### 1. 数据查询测试
- [ ] 输入有效车辆ID，能正确查询到检验记录数据
- [ ] 输入无效车辆ID，显示无数据提示
- [ ] 分页功能正常工作
- [ ] 查询结果格式正确

### 2. 数据插入测试
- [ ] 点击"插入"按钮，数据正确插入到表格
- [ ] 插入的数据格式正确
- [ ] 多次插入数据，表格行数正确增加
- [ ] 插入操作有成功提示

### 3. 表格保存测试
- [ ] 修改表格数据后保存，数据正确存储到数据库
- [ ] 保存操作有成功提示
- [ ] 重新查询时，保存的数据正确显示

### 4. 表格预览测试
- [ ] 预览页面正确显示保存的表格数据
- [ ] 表格配置正确应用（表头合并、列宽等）
- [ ] 表格为只读模式，不能编辑

### 5. 导出功能测试
- [ ] Word导出功能正常工作
- [ ] 导出的Word文档格式正确
- [ ] 导出的数据完整准确

## 常见问题排查

### 1. 后端问题
- 检查数据库连接配置
- 确认数据表已正确创建
- 查看后端日志错误信息

### 2. 前端问题
- 检查API请求路径是否正确
- 确认TableContainer组件正常工作
- 查看浏览器控制台错误信息

### 3. 数据问题
- 确认测试数据已正确插入
- 检查JSON格式是否正确
- 验证表格配置数据完整性

## 性能测试

### 1. 数据量测试
- 测试大量检验记录数据的查询性能
- 测试大型表格的渲染性能
- 测试Word导出的处理时间

### 2. 并发测试
- 多用户同时访问系统
- 多用户同时保存数据
- 多用户同时导出文档

## 部署验证

### 1. 生产环境部署
- 确认数据库配置正确
- 确认文件路径配置正确
- 确认网络访问正常

### 2. 功能验证
- 重复所有测试用例
- 验证数据持久化
- 验证系统稳定性
