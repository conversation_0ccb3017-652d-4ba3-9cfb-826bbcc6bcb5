# 简单Word设计器启动指南

## 项目概述
简单Word设计器是一个基于Vue2和SpringBoot的富文本编辑器，支持文本格式化、图片插入、页面设置和Word文档导出功能。

## 环境要求

### 前端环境
- Node.js 14+
- npm 或 pnpm
- Vue CLI

### 后端环境
- JDK 8
- Maven 3.6+
- MySQL 5.7+（如果需要数据库功能）

## 启动步骤

### 1. 启动后端服务

```bash
# 进入后端项目目录
cd logictrue-word

# 编译项目（首次运行或代码更改后）
mvn clean compile

# 启动Spring Boot应用
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动

### 2. 启动前端服务

```bash
# 进入前端项目目录
cd logictrue-ui-word

# 安装依赖（首次运行）
pnpm install
# 或者使用 npm install

# 启动开发服务器
pnpm run dev
# 或者使用 npm run dev
```

前端服务将在 `http://localhost:8081` 启动（如果8080被占用）

### 3. 访问应用

在浏览器中访问：`http://localhost:8081/simple-word-designer`

## 功能验证

### 基本功能测试
1. **页面加载**：确认页面正常加载，工具栏显示完整
2. **文本编辑**：输入文本，测试格式化功能
3. **图片插入**：上传并插入图片
4. **页面设置**：打开页面设置对话框，调整各项参数
5. **导出功能**：点击导出按钮，下载Word文档

### 快捷键测试
- `Ctrl + B`：加粗
- `Ctrl + I`：斜体
- `Ctrl + U`：下划线
- `Ctrl + S`：导出文档
- `Ctrl + P`：预览文档
- `Tab`：增加缩进
- `Shift + Tab`：减少缩进

### API接口测试
可以使用以下curl命令测试后端接口：

```bash
# 测试导出接口
curl -X POST http://localhost:8080/word/simpleWordExport/export \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试文档",
    "content": "<p>这是<strong>测试</strong>内容</p>",
    "pageSettings": {
      "paperSize": "A4",
      "orientation": "portrait",
      "showHeader": true,
      "headerText": "测试页眉"
    }
  }' \
  --output test_document.docx

# 测试预览接口
curl -X POST http://localhost:8080/word/simpleWordExport/preview \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试文档",
    "content": "<p>这是测试内容</p>"
  }'

# 测试验证接口
curl -X POST http://localhost:8080/word/simpleWordExport/validate \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试文档",
    "content": "<p>这是测试内容</p>"
  }'
```

## 常见问题解决

### 前端问题

1. **端口冲突**
   ```bash
   # 指定端口启动
   pnpm run dev --port 8082
   ```

2. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **API调用失败**
   - 检查后端服务是否启动
   - 检查网络请求URL是否正确
   - 查看浏览器控制台错误信息

### 后端问题

1. **编译失败**
   ```bash
   # 清理并重新编译
   mvn clean compile
   ```

2. **依赖下载失败**
   ```bash
   # 使用阿里云镜像
   mvn clean compile -Dmaven.repo.local=/path/to/local/repo
   ```

3. **Jsoup依赖问题**
   - 确认pom.xml中已添加Jsoup依赖
   - 版本号：1.15.3

4. **POI依赖问题**
   - 确认Apache POI相关依赖已正确配置
   - 检查版本兼容性

### 导出功能问题

1. **文件下载失败**
   - 检查浏览器是否阻止下载
   - 查看网络请求响应状态

2. **Word文档格式异常**
   - 检查HTML内容是否正确
   - 验证页面设置参数

3. **图片显示异常**
   - 确认图片是Base64格式
   - 检查图片大小限制

## 开发调试

### 前端调试
1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 使用Network面板检查API请求
4. 使用Vue DevTools调试组件状态

### 后端调试
1. 查看控制台日志输出
2. 使用IDE断点调试
3. 检查application.properties配置
4. 使用Postman测试API接口

## 性能优化建议

### 前端优化
- 大文档内容分页处理
- 图片压缩和懒加载
- 防抖处理用户输入

### 后端优化
- 添加请求参数验证
- 优化HTML解析性能
- 添加缓存机制

## 部署说明

### 前端部署
```bash
# 构建生产版本
pnpm run build:prod

# 部署到Web服务器
# 将dist目录内容复制到Web服务器根目录
```

### 后端部署
```bash
# 打包为JAR文件
mvn clean package

# 运行JAR文件
java -jar target/logictrue-word-*.jar
```

## 技术支持

如遇到问题，请：
1. 查看本文档的常见问题部分
2. 检查项目日志文件
3. 参考功能测试指南进行排查
4. 联系开发团队获取支持

## 更新日志

### v1.0.0
- 基本文本编辑功能
- 图片上传和插入
- 页面设置功能
- Word文档导出
- 快捷键支持
- 自动保存功能
- 帮助文档
