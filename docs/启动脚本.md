# 检验记录功能启动脚本

## 后端启动

### 方式一：使用Maven启动
```bash
cd /home/<USER>/nl-mes/word-nl/logictrue-word
mvn clean compile
mvn spring-boot:run
```

### 方式二：使用Java启动
```bash
cd /home/<USER>/nl-mes/word-nl/logictrue-word
mvn clean package -DskipTests
java -jar target/logictrue-word-*.jar
```

## 前端启动

```bash
cd /home/<USER>/nl-mes/word-nl/logictrue-ui-word
pnpm install
pnpm run dev
```

## 数据库初始化

### 1. 连接数据库
```bash
mysql -h 127.0.0.1 -P 3308 -u root -p
```

### 2. 选择数据库
```sql
USE lt-iot;
```

### 3. 创建表
```sql
-- 执行 docs/design_word_table.sql 中的建表语句
```

## 访问地址

- 前端地址：http://localhost:8080
- 后端API：http://localhost:9550
- 检验记录配置：http://localhost:8080/check-record-config
- 检验记录预览：http://localhost:8080/check-record-preview

## 测试数据

### 检验记录数据（模拟数据）
- 车辆ID：0822
- 包含脱漆、打磨、喷漆等工序记录

### 表格设计数据
- 车辆ID：0822
- 默认表格配置已在建表SQL中提供

## 快速测试流程

1. 启动后端服务
2. 启动前端服务
3. 访问配置页面：http://localhost:8080/check-record-config
4. 输入车辆ID：0822，点击查询
5. 点击记录的"插入"按钮，将数据插入表格
6. 点击"保存表格"按钮保存数据
7. 访问预览页面：http://localhost:8080/check-record-preview
8. 输入车辆ID：0822，查看保存的表格数据
9. 点击"导出Word"测试导出功能

## 故障排查

### 后端启动失败
- 检查Java版本（需要JDK 8）
- 检查数据库连接配置
- 检查端口9550是否被占用

### 前端启动失败
- 检查Node.js版本
- 执行 `pnpm install` 安装依赖
- 检查端口8080是否被占用

### 数据库连接失败
- 检查MySQL服务是否启动
- 检查数据库配置（host、port、username、password）
- 检查数据库lt-iot是否存在

### API调用失败
- 检查后端服务是否正常启动
- 检查网络连接
- 查看浏览器控制台错误信息
