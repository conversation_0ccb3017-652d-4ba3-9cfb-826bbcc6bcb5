<template>
  <div class="check-record-preview">
    <div class="preview-header">
      <h2>检验记录表预览</h2>
      <div class="header-actions">
        <div class="search-form">
          <label>车辆ID:</label>
          <input
            v-model="searchCarId"
            type="text"
            placeholder="请输入车辆ID"
            class="search-input"
            @keyup.enter="loadPreviewData"
          >
          <button @click="loadPreviewData" class="search-btn">查询</button>
        </div>
        <div class="action-buttons">
          <button @click="exportToWord" class="export-btn" :disabled="!hasData">导出Word</button>
          <button @click="refreshData" class="refresh-btn">刷新</button>
          <button @click="forceApplyTableConfig" class="debug-btn">重新应用配置</button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <p>正在加载数据...</p>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="!hasDesignData" class="no-data">
      <div class="no-data-icon">📋</div>
      <p>暂无数据</p>
      <p class="no-data-tip">请输入车辆ID查询对应的检验记录表</p>
    </div>

    <!-- 预览内容 -->
    <div v-else class="preview-content">
      <div class="preview-info">
        <div class="info-item">
          <span class="label">车辆ID:</span>
          <span class="value">{{ currentDesign.carId }}</span>
        </div>
        <div class="info-item">
          <span class="label">表格标题:</span>
          <span class="value">{{ currentDesign.title }}</span>
        </div>
        <div class="info-item">
          <span class="label">当前页面:</span>
          <span class="value">{{ currentDesign.pageName || '第1页' }}</span>
        </div>
        <div class="info-item">
          <span class="label">总页数:</span>
          <span class="value">{{ pageList.length }}页</span>
        </div>
        <div class="info-item">
          <span class="label">创建时间:</span>
          <span class="value">{{ formatDate(currentDesign.createTime) }}</span>
        </div>
        <div class="info-item">
          <span class="label">更新时间:</span>
          <span class="value">{{ formatDate(currentDesign.updateTime) }}</span>
        </div>
      </div>

      <!-- 页面切换区域 -->
      <div v-if="pageList.length > 1" class="page-navigation">
        <h4>页面切换</h4>
        <div class="page-tabs">
          <div
            v-for="page in pageList"
            :key="page.pageOrder"
            :class="['page-tab', { active: currentPageOrder === page.pageOrder }]"
            @click="switchToPage(page.pageOrder)"
          >
            <span class="page-name">{{ page.pageName }}</span>
            <span class="page-status" v-if="page.isActive === 1">●</span>
          </div>
        </div>
        <div class="page-info">
          <span>当前预览：{{ currentPageOrder }}/{{ pageList.length }}</span>
        </div>
      </div>

      <!-- 表格预览 -->
      <div class="table-preview">
        <!-- 数据状态提示 -->
        <div v-if="!hasData && hasDesignData" class="table-status">
          <p>表格配置已加载，暂无数据行</p>
        </div>
      </div>
    </div>

    <!-- 表格容器 - 始终渲染以确保ref可用 -->
    <TableContainer
      ref="tableContainerRef"
      :table-width="'100%'"
      :table-height="'600px'"
      :data-rows="previewTableData"
      v-show="hasDesignData"
      style="margin: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);"
    />

    <!-- 操作结果提示 -->
    <div v-if="operationResult" class="operation-result" :class="operationResult.success ? 'success' : 'error'">
      {{ operationResult.message }}
    </div>
  </div>
</template>

<script>
import TableContainer from '@/components/TableContainer.vue'
import {
  getDesignWordByCarId,
  getDesignWordPagesByCarId,
  getDesignWordByCarIdAndPage,
  getActiveDesignWordByCarId
} from '@/api/word/designWord'

export default {
  name: 'CheckRecordPreview',
  components: {
    TableContainer
  },
  data() {
    return {
      // 搜索车辆ID
      searchCarId: '0822',

      // 加载状态
      loading: false,

      // 当前设计数据
      currentDesign: null,

      // 预览表格数据
      previewTableData: [],

      // 表格配置
      tableConfig: null,

      // 操作结果
      operationResult: null,

      // 多页相关数据
      pageList: [], // 页面列表
      currentPageOrder: 1 // 当前预览页面
    }
  },
  computed: {
    hasData() {
      return this.currentDesign && this.previewTableData.length > 0
    },
    hasDesignData() {
      return !!this.currentDesign
    }
  },
  mounted() {
    console.log('CheckRecordPreview组件已挂载')

    // 等待一个tick确保所有子组件都已挂载
    this.$nextTick(() => {
      console.log('检查TableContainer组件:', {
        hasRef: !!this.$refs.tableContainerRef,
        refType: typeof this.$refs.tableContainerRef
      })

      // 初始加载数据
      if (this.searchCarId) {
        this.loadPreviewData()
      }
    })
  },
  methods: {
    /**
     * 加载预览数据
     */
    loadPreviewData() {
      if (!this.searchCarId || this.searchCarId.trim() === '') {
        this.showOperationResult(false, '请输入车辆ID')
        return
      }

      this.loading = true

      // 先加载页面列表
      this.loadPageList(this.searchCarId.trim()).then(() => {
        // 如果有页面，加载当前活动页面或第一页
        if (this.pageList.length > 0) {
          const activePage = this.pageList.find(page => page.isActive === 1)
          this.currentPageOrder = activePage ? activePage.pageOrder : 1

          this.loadPageData(this.currentPageOrder).then(() => {
            this.showOperationResult(true, '数据加载成功')
            this.loading = false
          }).catch(error => {
            console.error('加载页面数据失败:', error)
            this.showOperationResult(false, '加载页面数据失败')
            this.loading = false
          })
        } else {
          this.currentDesign = null
          this.previewTableData = []
          this.showOperationResult(false, '未找到对应车辆的检验记录表')
          this.loading = false
        }
      }).catch(error => {
        console.error('加载预览数据失败:', error)
        this.showOperationResult(false, '加载数据失败')
        this.currentDesign = null
        this.previewTableData = []
        this.pageList = []
        this.loading = false
      })
    },

    /**
     * 加载页面列表
     */
    loadPageList(carId) {
      return getDesignWordPagesByCarId(carId).then(response => {
        if (response.code === 200 && response.data) {
          this.pageList = response.data.sort((a, b) => a.pageOrder - b.pageOrder)
        } else {
          this.pageList = []
        }
      }).catch(error => {
        console.error('加载页面列表失败:', error)
        this.pageList = []
        throw error
      })
    },

    /**
     * 加载指定页面数据
     */
    loadPageData(pageOrder) {
      return getDesignWordByCarIdAndPage(this.searchCarId.trim(), pageOrder).then(response => {
        if (response.code === 200 && response.data) {
          this.currentDesign = response.data
          return this.parseAndRenderTable()
        } else {
          // 如果没有找到指定页面，尝试加载第一个页面
          if (this.pageList.length > 0) {
            const firstPage = this.pageList[0]
            return getDesignWordByCarIdAndPage(this.searchCarId.trim(), firstPage.pageOrder).then(fallbackResponse => {
              if (fallbackResponse.code === 200 && fallbackResponse.data) {
                this.currentDesign = fallbackResponse.data
                this.currentPageOrder = firstPage.pageOrder
                return this.parseAndRenderTable()
              }
            })
          }
        }
      }).catch(error => {
        console.error('加载页面数据失败:', error)
        this.currentDesign = null
        this.previewTableData = []
        throw error
      })
    },

    /**
     * 切换到指定页面
     */
    switchToPage(pageOrder) {
      if (pageOrder === this.currentPageOrder) {
        return
      }

      this.loading = true
      this.currentPageOrder = pageOrder

      this.loadPageData(pageOrder).then(() => {
        this.showOperationResult(true, `已切换到第${pageOrder}页`)
        this.loading = false
      }).catch(error => {
        console.error('切换页面失败:', error)
        this.showOperationResult(false, '切换页面失败')
        this.loading = false
      })
    },

    /**
     * 解析并渲染表格
     */
    parseAndRenderTable() {
      return new Promise((resolve, reject) => {
        try {
          console.log('开始解析表格数据:', this.currentDesign)

          // 解析表格配置
          if (this.currentDesign.tableConfig) {
            try {
              this.tableConfig = JSON.parse(this.currentDesign.tableConfig)
              console.log('成功解析tableConfig:', this.tableConfig)
            } catch (configError) {
              console.error('解析tableConfig失败:', configError)
              this.tableConfig = this.getDefaultTableConfig()
            }
          } else {
            console.log('没有tableConfig，使用默认配置')
            this.tableConfig = this.getDefaultTableConfig()
          }

          // 解析表格数据
          if (this.currentDesign.tableData) {
            try {
              const tableData = JSON.parse(this.currentDesign.tableData)
              console.log('解析的原始表格数据:', {
                type: Array.isArray(tableData) ? 'array' : typeof tableData,
                length: Array.isArray(tableData) ? tableData.length : 'N/A',
                firstRowSample: Array.isArray(tableData) && tableData.length > 0 ? tableData[0] : null
              })

              if (tableData && Array.isArray(tableData)) {
                // 检查数据格式，支持新旧格式
                let processedData = []

                // 如果是cellRows格式（包含行高信息），保持原格式
                if (tableData.length > 0 && tableData[0].length > 0 &&
                    typeof tableData[0][0] === 'object' && tableData[0][0].hasOwnProperty('content')) {
                  console.log('预览加载cellRows格式数据，包含行高信息，行数:', tableData.length)
                  // 确保数据格式完整，优先使用originContent
                  processedData = tableData.map(row =>
                    row.map(cell => {
                      const content = cell.originContent || cell.content || ''
                      return {
                        content: content,
                        originContent: cell.originContent || content,
                        isEditing: false,
                        originalContent: content,
                        hasMath: cell.hasMath || false,
                        mathML: cell.mathML || null,
                        hasMultipleContent: cell.hasMultipleContent || false,
                        mathMLMap: cell.mathMLMap || null,
                        width: cell.width,
                        height: cell.height
                      }
                    })
                  )
                } else {
                  console.log('预览加载简单格式数据，转换为标准格式，行数:', tableData.length)
                  // 旧格式，转换为标准格式
                  processedData = tableData.map(row =>
                    row.map(cellContent => ({
                      content: cellContent || '',
                      originContent: cellContent || '',
                      isEditing: false,
                      originalContent: cellContent || '',
                      hasMath: false,
                      mathML: null,
                      hasMultipleContent: false,
                      mathMLMap: null
                    }))
                  )
                }

                this.previewTableData = processedData
                console.log('处理后的预览数据:', {
                  rows: this.previewTableData.length,
                  firstRowSample: this.previewTableData.length > 0 ? this.previewTableData[0] : null
                })
              } else {
                console.log('表格数据为空或格式不正确')
                this.previewTableData = []
              }
            } catch (dataError) {
              console.error('解析tableData失败:', dataError)
              this.previewTableData = []
            }
          } else {
            console.log('没有tableData')
            this.previewTableData = []
          }

          // 等待DOM更新后应用配置
          this.$nextTick(() => {
            // 直接应用表格配置
            this.applyTableConfig()
            resolve()
          })

        } catch (error) {
          console.error('解析表格数据失败:', error)
          this.showOperationResult(false, '表格数据解析失败')
          reject(error)
        }
      })
    },

    /**
     * 获取默认表格配置
     */
    getDefaultTableConfig() {
      return {
        title: "检验记录表",
        headers: [
          [
            "检查工序\n名称",
            "检 查 项 目 及 技 术 条 件",
            "实 际 检 查 结 果",
            "完工",
            "",
            "操作员",
            "班组长",
            "检验员"
          ],
          [
            "",
            "",
            "",
            "月",
            "日",
            "",
            "",
            ""
          ]
        ],
        headerMerges: [
          {
            startRow: 0,
            startCol: 0,
            endRow: 1,
            endCol: 0,
            content: "检查工序\n名称"
          },
          {
            startRow: 0,
            startCol: 1,
            endRow: 1,
            endCol: 1,
            content: "检 查 项 目 及 技 术 条 件"
          },
          {
            startRow: 0,
            startCol: 2,
            endRow: 1,
            endCol: 2,
            content: "实 际 检 查 结 果"
          },
          {
            startRow: 0,
            startCol: 3,
            endRow: 0,
            endCol: 4,
            content: "完工"
          },
          {
            startRow: 0,
            startCol: 5,
            endRow: 1,
            endCol: 5,
            content: "操作员"
          },
          {
            startRow: 0,
            startCol: 6,
            endRow: 1,
            endCol: 6,
            content: "班组长"
          },
          {
            startRow: 0,
            startCol: 7,
            endRow: 1,
            endCol: 7,
            content: "检验员"
          }
        ],
        headerWidthConfig: {
          columnWidths: [100, 460, 160, 32, 32, 32, 32, 32],
          headerHeights: [35, 35]
        },
        verticalHeadersConfig: [false, false, false, false, false, true, true, true]
      }
    },

    /**
     * 应用表格配置
     */
    applyTableConfig() {
      const tableContainer = this.$refs.tableContainerRef
      if (!tableContainer) {
        console.error('表格组件未找到，请检查组件是否正确挂载')
        this.showOperationResult(false, '表格组件未找到，请刷新页面重试')
        return
      }

      console.log('TableContainer组件已找到:', {
        componentName: tableContainer.$options.name,
        hasMethods: {
          setDynamicHeaderConfig: typeof tableContainer.setDynamicHeaderConfig === 'function',
          insertDataFromJSON: typeof tableContainer.insertDataFromJSON === 'function'
        }
      })

      console.log('开始应用表格配置:', {
        hasTableConfig: !!this.tableConfig,
        hasPreviewData: !!(this.previewTableData && this.previewTableData.length > 0),
        tableConfig: this.tableConfig,
        previewDataRows: this.previewTableData ? this.previewTableData.length : 0
      })

      try {
        // 组装完整的JSON数据，参考json-table-demo.vue的insertJsonData方法
        const completeJsonData = {
          // 表头配置来源于tableConfig
          headers: this.tableConfig ? this.tableConfig.headers : null,
          headerMerges: this.tableConfig ? this.tableConfig.headerMerges : null,
          headerWidthConfig: this.tableConfig ? this.tableConfig.headerWidthConfig : null,
          verticalHeadersConfig: this.tableConfig ? this.tableConfig.verticalHeadersConfig : null,

          // 数据行来源于tableData
          cellRows: this.previewTableData || [],

          // 合并单元格配置
          merges: this.tableConfig ? (this.tableConfig.merges || []) : []
        }

        console.log('组装的完整JSON数据:', completeJsonData)

        // 检查是否有表头配置
        let headerConfig = null
        let headerWidthConfig = null
        let verticalHeadersConfig = null

        if (completeJsonData.headers && completeJsonData.headerMerges) {
          // 使用表头配置
          headerConfig = {
            headers: completeJsonData.headers,
            merges: completeJsonData.headerMerges
          }
          headerWidthConfig = completeJsonData.headerWidthConfig
          verticalHeadersConfig = completeJsonData.verticalHeadersConfig
        }

        if (headerConfig && headerWidthConfig) {
          // 设置动态表头配置
          tableContainer.setDynamicHeaderConfig(
            true,
            headerConfig,
            headerWidthConfig,
            verticalHeadersConfig
          )

          console.log('应用表头配置成功:', {
            headerConfig: headerConfig,
            headerWidthConfig: headerWidthConfig,
            verticalHeadersConfig: verticalHeadersConfig
          })
        } else {
          console.log('没有表头配置，使用默认表头')
          tableContainer.setDynamicHeaderConfig(false, null, null, null)
        }

        // 准备插入数据
        if (completeJsonData.cellRows && completeJsonData.cellRows.length > 0) {
          console.log('准备插入数据行，行数:', completeJsonData.cellRows.length)

          // 准备合并单元格配置
          const mergeCells = completeJsonData.merges || []
          const options = {
            clearExisting: true,
            validateData: false,
            startRow: 0,
            mergeCells
          }

          // 准备插入数据（确保使用cellRows格式）
          const insertData = {
            cellRows: completeJsonData.cellRows.map(row =>
              row.map(cellData => {
                // 确保数据格式完整
                const content = cellData.originContent || cellData.content || ''
                return {
                  content: content,
                  originContent: cellData.originContent || content,
                  hasMath: cellData.hasMath || false,
                  mathML: cellData.mathML || null,
                  width: cellData.width,
                  height: cellData.height
                }
              })
            )
          }

          console.log('准备插入的数据:', insertData)
          console.log('插入选项:', options)

          const result = tableContainer.insertDataFromJSON(insertData, options)

          if (result.success) {
            console.log('预览数据插入成功')
          } else {
            console.error('预览数据插入失败:', result.message)
            // 如果插入失败，回退到直接设置dataRows
            tableContainer.dataRows = this.previewTableData
          }
        } else {
          console.log('没有数据行，清空表格')
          // 如果没有数据，清空表格
          tableContainer.dataRows = []
        }

      } catch (error) {
        console.error('应用表格配置失败:', error)
        this.showOperationResult(false, '表格配置应用失败：' + error.message)
      }
    },

    /**
     * 刷新数据
     */
    refreshData() {
      this.loadPreviewData()
    },

    /**
     * 强制重新应用表格配置（调试用）
     */
    forceApplyTableConfig() {
      console.log('强制重新应用表格配置')
      if (this.hasDesignData) {
        this.$nextTick(() => {
          this.applyTableConfig()
          this.showOperationResult(true, '表格配置已重新应用')
        })
      } else {
        this.showOperationResult(false, '没有可应用的配置数据')
      }
    },


    /**
     * 导出Word文档
     */
    async exportToWord() {
      if (!this.hasData) {
        this.showOperationResult(false, '没有可导出的数据')
        return
      }

      try {
        this.showOperationResult(true, '正在准备导出...')

        const tableContainer = this.$refs.tableContainerRef
        if (!tableContainer) {
          this.showOperationResult(false, '表格组件未找到')
          return
        }

        // 调用表格组件的导出功能
        if (typeof tableContainer.exportToWord === 'function') {
          await tableContainer.exportToWord()
          this.showOperationResult(true, 'Word文档导出成功')
        } else {
          this.showOperationResult(false, '导出功能不可用')
        }

      } catch (error) {
        console.error('导出Word失败:', error)
        this.showOperationResult(false, '导出Word失败')
      }
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return '-'
      try {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      } catch (error) {
        return dateString
      }
    },

    /**
     * 显示操作结果
     */
    showOperationResult(success, message) {
      this.operationResult = { success, message }
      setTimeout(() => {
        this.operationResult = null
      }, 3000)
    }
  }
}
</script>

<style scoped>
.check-record-preview {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.preview-header {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-header h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 24px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-form label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.search-btn, .export-btn, .refresh-btn, .debug-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-btn {
  background: #007bff;
  color: white;
}

.search-btn:hover {
  background: #0056b3;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.export-btn {
  background: #28a745;
  color: white;
}

.export-btn:hover:not(:disabled) {
  background: #218838;
}

.export-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.refresh-btn {
  background: #17a2b8;
  color: white;
}

.refresh-btn:hover {
  background: #138496;
}

.debug-btn {
  background: #ffc107;
  color: #212529;
}

.debug-btn:hover {
  background: #e0a800;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* 无数据状态 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-data-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-data p {
  color: #666;
  font-size: 16px;
  margin: 10px 0;
  text-align: center;
}

.no-data-tip {
  font-size: 14px;
  color: #999;
}

/* 预览内容 */
.preview-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-item .label {
  font-weight: 500;
  color: #333;
  min-width: 80px;
}

.info-item .value {
  color: #666;
  flex: 1;
}

.table-preview {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.table-status {
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  color: #666;
  font-size: 14px;
}

/* 页面导航样式 */
.page-navigation {
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.page-navigation h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.page-navigation .page-tabs {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.page-navigation .page-tab {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  user-select: none;
}

.page-navigation .page-tab:hover {
  background: #f0f0f0;
  border-color: #007bff;
}

.page-navigation .page-tab.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.page-navigation .page-name {
  font-weight: 500;
}

.page-navigation .page-status {
  color: #28a745;
  font-size: 12px;
  margin-left: 5px;
}

.page-navigation .page-tab.active .page-status {
  color: rgba(255, 255, 255, 0.8);
}

.page-navigation .page-info {
  color: #666;
  font-size: 13px;
  text-align: center;
}

/* 操作结果提示 */
.operation-result {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.operation-result.success {
  background: #28a745;
}

.operation-result.error {
  background: #dc3545;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    min-width: auto;
  }

  .action-buttons {
    justify-content: center;
  }

  .preview-info {
    grid-template-columns: 1fr;
  }
}
</style>
