import request from '@/utils/request'

/**
 * 简单Word设计器API接口
 */

/**
 * 导出简单Word文档
 * @param {Object} data 导出数据
 * @param {string} data.title 文档标题
 * @param {string} data.content 文档内容（HTML格式）
 * @param {Object} data.pageSettings 页面设置
 */
export function exportSimpleWord(data) {
  return request({
    url: '/word/simpleWordExport/export',
    method: 'post',
    data: data,
    responseType: 'blob', // 重要：设置响应类型为blob以处理二进制文件
    timeout: 60000 // 设置60秒超时，因为导出可能需要较长时间
  })
}

/**
 * 预览简单Word文档
 * @param {Object} data 预览数据
 * @param {string} data.title 文档标题
 * @param {string} data.content 文档内容（HTML格式）
 * @param {Object} data.pageSettings 页面设置
 */
export function previewSimpleWord(data) {
  return request({
    url: '/word/simpleWordExport/preview',
    method: 'post',
    data: data,
    responseType: 'text', // 返回HTML文本
    timeout: 30000
  })
}

/**
 * 验证简单Word导出请求
 * @param {Object} data 验证数据
 * @param {string} data.title 文档标题
 * @param {string} data.content 文档内容（HTML格式）
 * @param {Object} data.pageSettings 页面设置
 */
export function validateExportRequest(data) {
  return request({
    url: '/word/simpleWordExport/validate',
    method: 'post',
    data: data
  })
}

/**
 * 下载文件的辅助函数
 * @param {Blob} blob 文件blob对象
 * @param {string} filename 文件名
 */
export function downloadFile(blob, filename) {
  // 创建下载链接
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename || 'document.docx'
  
  // 触发下载
  document.body.appendChild(link)
  link.click()
  
  // 清理
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * 导出并下载简单Word文档的便捷方法
 * @param {Object} data 导出数据
 * @param {string} filename 可选的文件名
 */
export async function exportAndDownloadSimpleWord(data, filename) {
  try {
    const response = await exportSimpleWord(data)
    
    // 从响应头中获取文件名（如果有的话）
    let downloadFilename = filename
    if (!downloadFilename && response.headers && response.headers['content-disposition']) {
      const disposition = response.headers['content-disposition']
      const filenameMatch = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch && filenameMatch[1]) {
        downloadFilename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''))
      }
    }
    
    // 如果仍然没有文件名，使用默认名称
    if (!downloadFilename) {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
      downloadFilename = `简单Word文档_${timestamp}.docx`
    }
    
    // 下载文件
    downloadFile(response.data, downloadFilename)
    
    return {
      success: true,
      filename: downloadFilename,
      size: response.data.size
    }
    
  } catch (error) {
    console.error('导出Word文档失败:', error)
    throw error
  }
}

/**
 * 获取页面设置的默认值
 */
export function getDefaultPageSettings() {
  return {
    paperSize: 'A4',
    orientation: 'portrait',
    marginTop: 25,
    marginBottom: 25,
    marginLeft: 30,
    marginRight: 30,
    showHeader: false,
    headerText: '',
    showFooter: false,
    footerText: '',
    showPageNumber: true,
    headerFooterFont: 'Microsoft YaHei',
    headerFooterFontSize: 12,
    headerFooterColor: '#666666'
  }
}

/**
 * 验证页面设置参数
 * @param {Object} pageSettings 页面设置对象
 * @returns {Object} 验证结果 {valid: boolean, errors: string[]}
 */
export function validatePageSettings(pageSettings) {
  const errors = []
  
  if (!pageSettings) {
    return { valid: true, errors: [] } // 页面设置是可选的
  }
  
  // 验证边距
  if (pageSettings.marginTop !== undefined && (pageSettings.marginTop < 0 || pageSettings.marginTop > 100)) {
    errors.push('上边距必须在0-100毫米之间')
  }
  
  if (pageSettings.marginBottom !== undefined && (pageSettings.marginBottom < 0 || pageSettings.marginBottom > 100)) {
    errors.push('下边距必须在0-100毫米之间')
  }
  
  if (pageSettings.marginLeft !== undefined && (pageSettings.marginLeft < 0 || pageSettings.marginLeft > 100)) {
    errors.push('左边距必须在0-100毫米之间')
  }
  
  if (pageSettings.marginRight !== undefined && (pageSettings.marginRight < 0 || pageSettings.marginRight > 100)) {
    errors.push('右边距必须在0-100毫米之间')
  }
  
  // 验证字体大小
  if (pageSettings.headerFooterFontSize !== undefined && 
      (pageSettings.headerFooterFontSize < 8 || pageSettings.headerFooterFontSize > 72)) {
    errors.push('页眉页脚字体大小必须在8-72之间')
  }
  
  // 验证纸张大小
  const validPaperSizes = ['A4', 'A3', 'Letter', 'Custom']
  if (pageSettings.paperSize && !validPaperSizes.includes(pageSettings.paperSize)) {
    errors.push('无效的纸张大小')
  }
  
  // 验证页面方向
  const validOrientations = ['portrait', 'landscape']
  if (pageSettings.orientation && !validOrientations.includes(pageSettings.orientation)) {
    errors.push('无效的页面方向')
  }
  
  return {
    valid: errors.length === 0,
    errors: errors
  }
}

/**
 * 格式化文件大小显示
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小字符串
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 清理HTML内容，移除不安全的标签和属性
 * @param {string} html HTML内容
 * @returns {string} 清理后的HTML内容
 */
export function sanitizeHtml(html) {
  if (!html) return ''
  
  // 创建临时DOM元素
  const temp = document.createElement('div')
  temp.innerHTML = html
  
  // 移除script标签
  const scripts = temp.querySelectorAll('script')
  scripts.forEach(script => script.remove())
  
  // 移除危险的事件属性
  const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur']
  const allElements = temp.querySelectorAll('*')
  allElements.forEach(element => {
    dangerousAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        element.removeAttribute(attr)
      }
    })
  })
  
  return temp.innerHTML
}

/**
 * 将HTML内容转换为纯文本
 * @param {string} html HTML内容
 * @returns {string} 纯文本内容
 */
export function htmlToText(html) {
  if (!html) return ''
  
  const temp = document.createElement('div')
  temp.innerHTML = html
  return temp.textContent || temp.innerText || ''
}

/**
 * 检查内容是否为空
 * @param {string} content 内容字符串
 * @returns {boolean} 是否为空
 */
export function isContentEmpty(content) {
  if (!content) return true
  
  // 移除HTML标签后检查是否为空
  const textContent = htmlToText(content).trim()
  return textContent.length === 0
}
