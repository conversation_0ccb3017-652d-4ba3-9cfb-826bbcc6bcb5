package com.logictrue.word.controller;

import com.logictrue.common.core.web.controller.BaseController;
import com.logictrue.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 检验记录数据查询Controller
 */
@RestController
@RequestMapping("/getCheckRecord")
@Api(value = "检验记录数据查询", tags = "检验记录数据查询")
public class CheckRecordController extends BaseController {

    /**
     * 分页查询检验记录数据
     */
    @GetMapping
    @ApiOperation(value = "分页查询检验记录数据")
    public AjaxResult getCheckRecord(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("车辆ID") @RequestParam(required = false) String carId) {
        
        try {
            // 模拟数据，实际应该从数据库查询
            List<Map<String, Object>> records = new ArrayList<>();
            
            // 根据carId过滤数据
            if ("0822".equals(carId)) {
                Map<String, Object> record1 = new HashMap<>();
                record1.put("check_name", "脱漆");
                record1.put("check_content", "1.去除浮漆");
                record1.put("result", "完成");
                record1.put("month_str", 8);
                record1.put("day_str", 22);
                record1.put("check_user_name", "张三");
                record1.put("bzz", "张三");
                record1.put("jyy", "张三");
                records.add(record1);
                
                Map<String, Object> record2 = new HashMap<>();
                record2.put("check_name", "脱漆");
                record2.put("check_content", "2.用水冲洗");
                record2.put("result", "完成");
                record2.put("month_str", 8);
                record2.put("day_str", 22);
                record2.put("check_user_name", "张三");
                record2.put("bzz", "张三");
                record2.put("jyy", "张三");
                records.add(record2);
                
                Map<String, Object> record3 = new HashMap<>();
                record3.put("check_name", "打磨");
                record3.put("check_content", "1.表面打磨处理");
                record3.put("result", "进行中");
                record3.put("month_str", 8);
                record3.put("day_str", 23);
                record3.put("check_user_name", "李四");
                record3.put("bzz", "李四");
                record3.put("jyy", "王五");
                records.add(record3);
                
                Map<String, Object> record4 = new HashMap<>();
                record4.put("check_name", "喷漆");
                record4.put("check_content", "1.底漆喷涂");
                record4.put("result", "待开始");
                record4.put("month_str", 8);
                record4.put("day_str", 24);
                record4.put("check_user_name", "赵六");
                record4.put("bzz", "赵六");
                record4.put("jyy", "钱七");
                records.add(record4);
            }
            
            // 分页处理
            int total = records.size();
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, total);
            
            List<Map<String, Object>> pageRecords = new ArrayList<>();
            if (start < total) {
                pageRecords = records.subList(start, end);
            }
            
            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pageRecords);
            result.put("total", total);
            result.put("size", pageSize);
            result.put("current", pageNum);
            result.put("pages", (total + pageSize - 1) / pageSize);
            
            return AjaxResult.success(result);
            
        } catch (Exception e) {
            logger.error("查询检验记录数据失败", e);
            return AjaxResult.error("查询检验记录数据失败：" + e.getMessage());
        }
    }
}
