package com.logictrue.word.dto;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 简单Word导出请求DTO
 */
@Data
public class SimpleWordExportRequest {

    /**
     * 文档标题
     */
    private String title;

    /**
     * JSON结构化内容 - 包含所有文档内容和图片
     */
    private List<ContentNode> jsonContent;

    /**
     * 页面设置
     */
    private PageSettings pageSettings;

    /**
     * 文档元数据
     */
    private Metadata metadata;

    /**
     * 导出配置
     */
    private ExportConfig exportConfig;

    /**
     * 内容节点类
     */
    @Data
    public static class ContentNode {
        /**
         * 节点ID
         */
        private String id;

        /**
         * 节点类型（p, div, img, span等）
         */
        private String type;

        /**
         * 文本内容
         */
        private String content;

        /**
         * CSS样式
         */
        private Map<String, String> styles;

        /**
         * HTML属性
         */
        private Map<String, String> attributes;

        /**
         * 子节点
         */
        private List<ContentNode> children;
    }

    /**
     * 文档元数据类
     */
    @Data
    public static class Metadata {
        /**
         * 创建时间
         */
        private String created;

        /**
         * 修改时间
         */
        private String modified;

        /**
         * 版本号
         */
        private String version;

        /**
         * 作者
         */
        private String author;

        /**
         * 描述
         */
        private String description;
    }

    /**
     * 导出配置类
     */
    @Data
    public static class ExportConfig {
        /**
         * 是否包含图片
         */
        private Boolean includeImages = true;

        /**
         * 图片格式（base64, url等）
         */
        private String imageFormat = "base64";

        /**
         * 是否保持布局
         */
        private Boolean preserveLayout = true;

        /**
         * 图片最大宽度（像素）
         */
        private Integer maxImageWidth = 600;

        /**
         * 图片最大高度（像素）
         */
        private Integer maxImageHeight = 800;
    }
    
    /**
     * 页面设置类
     */
    @Data
    public static class PageSettings {
        /**
         * 纸张大小
         */
        private String paperSize = "A4";
        
        /**
         * 页面方向
         */
        private String orientation = "portrait";
        
        /**
         * 上边距（毫米）
         */
        private Integer marginTop = 25;
        
        /**
         * 下边距（毫米）
         */
        private Integer marginBottom = 25;
        
        /**
         * 左边距（毫米）
         */
        private Integer marginLeft = 30;
        
        /**
         * 右边距（毫米）
         */
        private Integer marginRight = 30;
        
        /**
         * 是否显示页眉
         */
        private Boolean showHeader = false;
        
        /**
         * 页眉内容
         */
        private String headerText = "";
        
        /**
         * 是否显示页脚
         */
        private Boolean showFooter = false;
        
        /**
         * 页脚内容
         */
        private String footerText = "";
        
        /**
         * 是否显示页码
         */
        private Boolean showPageNumber = true;
        
        /**
         * 页眉页脚字体
         */
        private String headerFooterFont = "Microsoft YaHei";
        
        /**
         * 页眉页脚字体大小
         */
        private Integer headerFooterFontSize = 12;
        
        /**
         * 页眉页脚字体颜色
         */
        private String headerFooterColor = "#666666";
    }
}
