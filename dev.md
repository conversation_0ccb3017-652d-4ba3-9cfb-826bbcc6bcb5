### 分页查询数据接口,carId为查询条件车辆id，使用文本框输入
- /word/getCheckRecord?pageNum=1&pageSize=10&carId=0822

### 查询返回数据格式
```json
[
        {
            "check_name": "脱漆",
            "check_content": "1.去除浮漆",
            "result": "完成",
            "month_str": 8,
            "day_str": 22,
            "check_user_name": "张三",
            "bzz": "张三",
            "jyy": "张三"
        },
        {
            "check_name": "脱漆",
            "check_content": "2.用水冲洗",
            "result": "完成",
            "month_str": 8,
            "day_str": 22,
            "check_user_name": "张三",
            "bzz": "张三",
            "jyy": "张三"
        }
    ]
```
### 表格配置
```json
{
  "title": "检验记录表",
  "headers": [
    [
      "检查工序\n名称",
      "检 查 项 目 及 技 术 条 件",
      "实 际 检 查 结 果",
      "完工",
      "",
      "操作员",
      "班组长",
      "检验员"
    ],
    [
      "",
      "",
      "",
      "月",
      "日",
      "",
      "",
      ""
    ]
  ],
  "cellRows": [],
  "merges": [],
  "headerMerges": [
    {
      "startRow": 0,
      "startCol": 0,
      "endRow": 1,
      "endCol": 0,
      "content": "检查工序\n名称"
    },
    {
      "startRow": 0,
      "startCol": 1,
      "endRow": 1,
      "endCol": 1,
      "content": "检 查 项 目 及 技 术 条 件"
    },
    {
      "startRow": 0,
      "startCol": 2,
      "endRow": 1,
      "endCol": 2,
      "content": "实 际 检 查 结 果"
    },
    {
      "startRow": 0,
      "startCol": 3,
      "endRow": 0,
      "endCol": 4,
      "content": "完工"
    },
    {
      "startRow": 0,
      "startCol": 5,
      "endRow": 1,
      "endCol": 5,
      "content": "操作员"
    },
    {
      "startRow": 0,
      "startCol": 6,
      "endRow": 1,
      "endCol": 6,
      "content": "班组长"
    },
    {
      "startRow": 0,
      "startCol": 7,
      "endRow": 1,
      "endCol": 7,
      "content": "检验员"
    }
  ],
  "headerWidthConfig": {
    "columnWidths": [
      100,
      460,
      160,
      32,
      32,
      32,
      32,
      32
    ],
    "headerHeights": [
      35,
      35
    ]
  },
  "verticalHeadersConfig": [
    false,
    false,
    false,
    false,
    false,
    true,
    true,
    true
  ],
  "metadata": {
    "exportTime": "2025-08-24T04:46:09.128Z",
    "totalRows": 0,
    "totalColumns": 8,
    "headerRows": 2,
    "hasMergedCells": false,
    "hasHeaderMerges": true,
    "hasLatexProcessing": true,
    "useDynamicHeader": true,
    "hasCustomWidth": [
      100,
      460,
      160,
      32,
      32,
      32,
      32,
      32
    ],
    "hasVerticalHeaders": true
  }
}
```

### 开发要求
- 前端路径/home/<USER>/nl-mes/word-nl/logictrue-ui-word
- 后端路径/home/<USER>/nl-mes/word-nl/logictrue-word
- 页面布局为左右布局，左边查询分页接口展示数据，右边根据表格配置渲染表格
- 表格渲染参考/home/<USER>/nl-mes/word-nl/logictrue-ui-word/src/views/test/json-table-demo.vue
- 分页查询出来的数据每一行最后增加操作项插入，点击将接口数据插入表格中
- 右边的表格数据需要能进行保存和修改，整体保存为json数据
- 设计表名为design_word的表保存表格数据
- 创建两个新文件，一个为配置界面，一个为预览界面
- 预览界面查询design_word表数据，根据配置渲染表格
- 完成前端页面，后端接口开发
